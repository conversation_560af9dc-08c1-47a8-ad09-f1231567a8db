import { CommonModule } from '@angular/common';
import { Component, inject, signal } from '@angular/core';
import { Router } from '@angular/router';
import {
  ButtonComponent,
  DialogService,
  IconComponent,
} from '@ava/play-comp-library';
import { PageFooterComponent, TokenStorageService } from '@shared/index';
import {
  Agent,
  AgentCardAction,
  AgentCardComponent,
} from 'projects/elder-wand/src/app/shared/components/agent-card/agent-card.component';
import { FilterTabsComponent } from 'projects/elder-wand/src/app/shared/components/filter-tabs/filter-tabs.component';
import { GlobalStoreService } from 'projects/elder-wand/src/app/shared/service/global-store.service';
import { AgentService } from 'projects/elder-wand/src/app/shared/services/agent.service';
import { FilterTab } from '../../shared/interfaces/launchpad.interface';

interface User {
  name: string;
  role: string;
  type: string;
}

interface StatCard {
  icon: string;
  title: string;
  value: string;
  iconColor: string;
}

interface AgentCard {
  id: number;
  title: string;
  description: string;
  status: 'Approved' | 'Pending' | 'Denied' | 'Draft';
  createdDate: string;
}

type AgentStatus = 'all' | 'approved' | 'pending' | 'denied' | 'draft';

@Component({
  selector: 'app-view-account',
  imports: [
    CommonModule,
    FilterTabsComponent,
    ButtonComponent,
    PageFooterComponent,
    AgentCardComponent,
  ],
  templateUrl: './view-account.component.html',
  styleUrl: './view-account.component.scss',
})
export class ViewAccountComponent {
  userName: string = '';
  userEmail: string = '';
  userRole: string = 'Lead Backend Engineer';
  userAvatar: string = 'assets/icons/user-avatar.svg';
  selectedUser: User | null = null;
  // currentPage: number = 1;
  // itemsPerPage: number = 12;

  // Statistics cards data
  statCards: StatCard[] = [
    {
      icon: 'star',
      title: 'Average Rating',
      value: '4.8',
      iconColor: 'white',
    },
    {
      icon: 'chart-spline',
      title: 'Average Accuracy',
      value: '97%',
      iconColor: 'white',
    },
  ];

  // Tabs data for My Agents section
  activeTab = 'all';
  agentTabs: FilterTab[] = [
    { id: 'all', label: 'All', priority: 100 },
    {
      id: 'approved',
      label: 'Approved',
      icon: 'circle-check',
      iconColor: '#059669',
      priority: 90,
    },
    {
      id: 'pending',
      label: 'Pending (10)',
      icon: 'clock',
      iconColor: '#D97706',
      priority: 80,
    },
    {
      id: 'denied',
      label: 'Denied (4)',
      icon: 'x',
      iconColor: '#DC2626',
      priority: 70,
    },
    { id: 'draft', label: 'Draft (1)', icon: 'pencil', priority: 60 },
  ];

  // Agent cards data
  allAgentCards: Agent[] = [];
  status: AgentStatus = 'all';
  currentPage = signal<number>(1);
  itemsPerPage = signal<number>(9);
  totalItem = signal<number>(0);
  cardSkeletonPlaceholders = Array(11);
  totalCount: { [key: string]: string } = {
    all: 'totalCount',
    approved: 'approvedCount',
    pending: 'pendingCount',
    denied: 'rejectedCount',
    draft: 'draftedCount',
  };
  totalAgents!: number;
  approvedAgents!: number;

  isLoading: boolean = false;
  isMarketplace: boolean = false; // Set to false for dashboard view
  showTwoColumns: boolean = false;

  // Filtered agent cards based on active tab
  get filteredAgentCards(): Agent[] {
    const startIndex = (this.currentPage() - 1) * this.itemsPerPage();
    const endIndex = startIndex + this.itemsPerPage();

    return this.getFilteredCards().slice(startIndex, endIndex);
  }

  // Get all filtered cards without pagination
  private getFilteredCards(): Agent[] {
    if (this.activeTab === 'all') {
      return this.allAgentCards;
    }

    const statusMap: { [key: string]: string } = {
      approved: 'Approved',
      pending: 'Pending',
      denied: 'Denied',
      draft: 'Draft',
    };

    const targetStatus = statusMap[this.activeTab];
    return this.allAgentCards.filter((card) => card.status === targetStatus);
  }

  private tokenStorageService = inject(TokenStorageService);
  private globalStoreService = inject(GlobalStoreService);
  private router = inject(Router);
  private agentService = inject(AgentService);
  private dialogService = inject(DialogService);

  ngOnInit(): void {
    this.loadUserData();
    this.loadSelectedUser();
    this.getAllAgents();
    this.getAgetMetrics();
    this.generateUserAvatar();
  }

  getSkeletonCards(): number[] {
    return Array(8)
      .fill(0)
      .map((_, i) => i);
  }

  onAgentClick(agent: Agent): void {
    console.log('Agent clicked:', agent);
    // Handle agent card click - navigate to details, etc.
  }

  onAgentAction(action: AgentCardAction): void {
    console.log('Agent action:', action);

    switch (action.type) {
      case 'view':
        break;
      case 'edit':
        break;
      case 'delete':
        break;
      case 'play':
        break;
    }
  }

  getAgetMetrics() {
    this.agentService.getAgentMertrics().subscribe({
      next: (res: any) => {
        this.totalAgents = res?.agentsMetrics?.totalCount ?? 0;
        this.approvedAgents = res?.agentsMetrics?.approvedCount ?? 0;
      },
      error: (e) => console.error(e),
    });
  }

  getAllAgents() {
    this.isLoading = true;
    const payload = {
      page: this.currentPage(),
      records: this.itemsPerPage(),
      status: this.status === 'denied' ? 'rejected' : this.status,
    };
    this.agentService.getAllAgents(payload).subscribe({
      next: (res: any) => {
        this.allAgentCards = res?.agentDetails.map((item: any) => ({
          id: String(item.id),
          title: item.name,
          description: item.agentDetails,
          rating: 4.5, // Default rating
          studio: {
            name: 'Experience Studio',
            type: 'Experience Studio',
            backgroundColor: '#FFF4F9',
            textColor: '#DC047B',
          },
          users: Math.floor(Math.random() * 100) + 10, // Random users
          status:
            item.status === 'IN_REVIEW'
              ? 'Pending'
              : item.status === 'APPROVED'
                ? 'Approved'
                : item.status === 'CREATED'
                  ? 'Draft'
                  : item.status === 'REJECTED'
                    ? 'Denied'
                    : item.status,
          createdDate: item.createdAt.split('T')[0], // takes only the date part (YYYY-MM-DD)
        }));

        this.agentTabs = this.agentTabs.map((tab) => {
          if (tab.id === 'approved') {
            return {
              ...tab,
              label: `Approved (${res?.userAgentsMetrics?.approvedCount})`,
            };
          }
          if (tab.id === 'pending') {
            return {
              ...tab,
              label: `Pending (${res?.userAgentsMetrics?.pendingCount})`,
            };
          }
          if (tab.id === 'denied') {
            return {
              ...tab,
              label: `Denied (${res?.userAgentsMetrics?.rejectedCount})`,
            };
          }
          if (tab.id === 'draft') {
            return {
              ...tab,
              label: `Draft (${res?.userAgentsMetrics?.draftedCount})`,
            };
          }
          return tab;
        });
        this.totalItem.set(
          res?.userAgentsMetrics[this.totalCount[this.status]] ?? 0,
        );
      },
      error: (e) => console.error(e),
      complete: () => (this.isLoading = false),
    });
  }

  private loadUserData(): void {
    this.userName = this.tokenStorageService.getDaName() || 'Akash Kumar';
    this.userEmail =
      this.tokenStorageService.getDaUsername() || '<EMAIL>';
  }

  private loadSelectedUser(): void {
    this.globalStoreService.selectedUser.subscribe((user) => {
      this.selectedUser = user;
      if (user && user.role) {
        this.userRole = user.role;
      }
    });
  }

  onCreateAgent(): void {
    this.router.navigate(['/build/agents/individual']);
  }

  onViewAnalytics(): void {
    console.log('View Analytics clicked');
    // TODO: Implement navigation to analytics page
  }

  onEditAgent(agent: AgentCard): void {
    this.router.navigate(['/build/agents/collaborative'], {
      queryParams: {
        id: agent.id,
        mode: 'edit',
      },
    });
  }

  onViewAgent(agent: AgentCard): void {
    this.router.navigate(['/build/agents/collaborative'], {
      queryParams: {
        id: agent.id,
        mode: 'view',
      },
    });
  }

  onDeleteAgent(agent: AgentCard): void {
    this.dialogService
      .confirmation({
        title: 'Delete Agent',
        message: `Are you sure you want to delete "${agent.title || 'this agent'}"? This action cannot be undone.`,
        confirmButtonText: 'Delete',
        cancelButtonText: 'Cancel',
        destructive: true,
      })
      .then((result) => {
        if (result.confirmed) {
          this.deleteAgent(agent.id);
        }
      });
  }

  private deleteAgent(agentId: number): void {
    // Show loading dialog
    const loadingDialog = this.dialogService.loading({
      title: 'Deleting Agent',
      message: 'Please wait while we delete the agent...',
      showProgress: true,
    });

    this.agentService.deleteCollaborativeAgent(agentId).subscribe({
      next: (response: any) => {
        this.getAllAgents();
        // Close loading dialog
        this.dialogService.close();

        // Show success dialog
        this.dialogService
          .success({
            title: 'Agent Deleted',
            message:
              response?.message || `Agent has been deleted successfully.`,
          })
          .then(() => {
            console.log('Agent deletion completed');
          });
      },
      error: (error: any) => {
        console.error('Error deleting agent:', error);

        // Close loading dialog
        this.dialogService.close();

        // Show error dialog with retry option
        this.dialogService
          .error({
            title: 'Error',
            message:
              error?.error?.message ||
              'Failed to delete agent. Please try again.',
            showRetryButton: true,
            retryButtonText: 'Ok',
          })
          .then((result) => {
            if (result.action === 'retry') {
              this.deleteAgent(agentId); // Retry deletion
            }
          });
      },
    });
  }

  onPlayAgent(agent: AgentCard): void {
    this.router.navigate(['/build/agents/collaborative/execute'], {
      queryParams: {
        id: agent.id,
      },
    });
  }

  getStatusClass(status: string): string {
    const statusClasses: { [key: string]: string } = {
      Approved: 'status-approved',
      Pending: 'status-pending',
      Denied: 'status-denied',
      Draft: 'status-draft',
    };
    return statusClasses[status] || 'status-default';
  }

  get totalItemsForPagination(): number {
    return this.getFilteredCards().length;
  }

  get shouldShowPagination(): boolean {
    return this.totalItemsForPagination > this.itemsPerPage();
  }

  onPageChange(page: number): void {
    this.currentPage.set(page);
    this.getAllAgents();
  }

  onTabChange(tabId: string): void {
    this.activeTab = tabId;
    this.status = tabId as AgentStatus;
    this.currentPage.set(1);
    this.getAllAgents();
  }

  private generateUserAvatar(): void {
    // Generate avatar from user initials if no profile picture is available
    if (this.userName) {
      const nameParts = this.userName.trim().split(' ');
      let initials = '';

      if (nameParts.length >= 2) {
        // First letter of first name and first letter of last name
        initials = nameParts[0][0] + nameParts[nameParts.length - 1][0];
      } else if (nameParts.length === 1) {
        // Just first letter if only one name
        initials = nameParts[0][0];
      } else {
        initials = 'U'; // Default to 'U' for User
      }

      initials = initials.toUpperCase();

      // Generate a colored avatar with initials
      const colors = [
        '#8B5CF6',
        '#06B6D4',
        '#10B981',
        '#F59E0B',
        '#EF4444',
        '#8B5A2B',
        '#6366F1',
        '#EC4899',
      ];
      const colorIndex = this.userName.length % colors.length;
      const backgroundColor = colors[colorIndex];

      this.userAvatar = `data:image/svg+xml;base64,${btoa(`
        <svg width="40" height="40" xmlns="http://www.w3.org/2000/svg">
          <circle cx="20" cy="20" r="20" fill="${backgroundColor}"/>
          <text x="20" y="26" font-family="Inter, Arial, sans-serif" font-size="14" font-weight="600" fill="white" text-anchor="middle">${initials}</text>
        </svg>
      `)}`;
    }
  }
}

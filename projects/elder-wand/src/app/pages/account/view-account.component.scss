.my-agent-home-container {
  margin: 0 auto;
}

.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 12px;
  padding: 2rem;
  margin-bottom: 1rem;
}

.user-profile-section {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.user-avatar {
  .avatar-image {
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
    box-shadow: 0 2px 8px rgba(20, 70, 146, 0.08);
    margin-bottom: 26px;
  }
}

.user-details {
  .user-name {
    color: #000;
    font-size: 20px;
    font-weight: 600;
    margin: 0;
  }

  .user-email,
  .user-role {
    color: #616874;
    font-size: 14px;
    font-weight: 400;
    margin: 0;
  }
}

.action-buttons {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.create-agent-btn {
  .ava-button .button-label {
    color: #fff;
  }
}

// Statistics Cards Section
.stats-section {
  margin: 2rem;

  .row {
    margin: 0 -0.5rem;
  }

  .col-12,
  .col-sm-6,
  .col-lg-3 {
    padding: 0 0.5rem;
  }
}

.stat-card {
  background: #fff;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  border: 1px solid var(--Brand-Neutral-n-50, #f0f1f2);
  box-shadow: var(--Elevation-01X) var(--Elevation-01Y) var(--Elevation-01Blur)
    var(--Elevation-01Spread) var(--BrandNeutraln-50);

  &:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  }
}

.stat-header {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.stat-icon-container {
  flex-shrink: 0;

  .stat-icon {
    svg {
      stroke: black !important;
    }
  }
}

.stat-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
  flex: 1;
}

.stat-title {
  color: #000;
  text-align: left;
  font-size: 16px;
  font-weight: 400;
  margin: 0;
}

.stat-value-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.rating-star {
  flex-shrink: 0;
}

.stat-value {
  color: #3b3f46;
  font-size: 32px;
  font-weight: 700;
  margin: 0;
}

// Responsive design
@media (max-width: 768px) {
  .header-section {
    flex-direction: column;
    gap: 1.5rem;
    text-align: center;
  }

  .action-buttons {
    flex-direction: column;
    width: 100%;

    ava-button {
      width: 100%;
      min-width: unset;
    }
  }

  .stat-card {
    padding: 1rem;
  }

  .stat-value {
    font-size: 1.5rem;
  }
}

@media (max-width: 576px) {
  .stat-card {
    gap: 0.75rem;
  }

  .stat-title {
    font-size: 0.8rem;
  }

  .stat-value {
    font-size: 1.25rem;
  }
}

// My Agents Section
.my-agents-section {
  margin: 2rem 2rem 0 2rem;
  .section-title {
    color: #000;
    font-size: 32px;
    font-weight: 700;
    margin: 0.5rem 0;
  }
}

// Agent Cards Grid
.agent-cards-grid {
  margin-top: 1rem;

  .row {
    margin: 0 -0.5rem;
  }

  .col-12,
  .col-md-6,
  .col-lg-4 {
    padding: 0 0.5rem;
    margin-bottom: 1.5rem;
  }
}

.agent-card {
  background: #fff;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
    border-color: #e0e0e0;
  }

  .card-content {
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 16px;
    gap: 12px;

    .agent-title {
      flex: 1;
      font-size: 16px;
      font-weight: 600;
      color: var(--Colors-Text-secondary, #616874);
      margin: 0;
      line-height: 1.4;

      // Text truncation with ellipsis
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: calc(100% - 80px); // Reserve space for status badge
    }

    .status-badge {
      flex-shrink: 0;
      padding: 6px 12px;
      border-radius: 20px;
      font-size: 12px;
      font-weight: 500;
      text-transform: capitalize;

      &.status-approved {
        color: #059669;
      }

      &.status-pending {
        color: #616874;
      }

      &.status-denied {
        color: #dc2626;
      }

      &.status-draft {
        color: #dc2626;
      }
    }
  }

  .agent-description {
    flex: 1;
    font-size: 14px;
    color: #616874;
    line-height: 1.5;
    margin: 0 0 20px 0;

    // Multi-line text truncation
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .card-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: auto;
    gap: 16px;

    .meta-info {
      display: flex;
      flex-direction: column;
      gap: 8px;
      flex: 1;

      .meta-item {
        display: flex;
        align-items: center;
        gap: 8px;

        .meta-text {
          font-size: 12px;
          color: var(--Colors-Text-secondary, #616874);
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }

    .action-buttons {
      display: flex;
      gap: 8px;
      flex-shrink: 0;

      .action-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 32px;
        height: 32px;
        border: none;
        border-radius: 8px;
        background: transparent;
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover {
          background-color: #f3f4f6;
          transform: scale(1.05);
        }

        &.primary {
          background-color: #e91e63;
          color: white;

          &:hover {
            background-color: #d81b60;
            transform: scale(1.05);
          }
        }
      }
    }
  }
}
.pagination {
  margin: 0 2rem 1rem 2rem;
  padding: 0;
}
.page-footer {
  margin: 0;
  padding: 0;
}
.page-container {
  padding: 0;
}

// Responsive styles for agent cards
@media (max-width: 768px) {
  .agent-cards-grid {
    .col-12,
    .col-md-6,
    .col-lg-4 {
      margin-bottom: 1rem;
    }
  }

  .agent-card {
    padding: 1rem;

    .card-layout {
      gap: 0.75rem;
    }

    .agent-icon {
      width: 40px;
      height: 40px;
    }

    .agent-title {
      font-size: 16px;
    }

    .agent-description {
      font-size: 14px;
    }

    .agent-meta .created-date {
      font-size: 14px;
    }

    .status-badge {
      padding: 0.2rem 0.5rem;
      font-size: 0.7rem;
    }

    .card-actions {
      gap: 0.4rem;
      flex-direction: column;

      ava-button {
        width: 100%;

        &:last-child {
          width: 40px;
          align-self: flex-end;
        }
      }
    }
  }
}

// Medium screens
@media (max-width: 992px) {
  .agent-card {
    .card-actions {
      ava-button {
        width: 100%;

        &:last-child {
          width: 44px;
        }
      }
    }
  }
}

// Additional responsive breakpoints for agent cards
@media (max-width: 576px) {
  .agent-card {
    padding: 0.75rem;

    .card-layout {
      gap: 0.5rem;
    }

    .agent-icon {
      width: 36px;
      height: 36px;
    }

    .agent-title {
      font-size: 16px;
    }

    .agent-description {
      font-size: 13px;
    }

    .agent-meta .created-date {
      font-size: 12px;
    }

    .card-actions {
      gap: 0.3rem;

      ava-button {
        font-size: 12px;
        height: 36px;

        &:last-child {
          width: 36px;
          height: 36px;
        }
      }
    }
  }
}

// Skeleton Loader Styles
.skeleton-loader {
  padding: 24px;

  .skeleton-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;

    .skeleton-category {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .skeleton-value {
      display: flex;
      align-items: center;
      gap: 6px;
    }
  }

  .skeleton-body {
    margin-bottom: 60px;

    .skeleton-title {
      margin-bottom: 16px;
    }
  }

  .skeleton-footer {
    display: flex;
    align-items: flex-end;
    justify-content: space-between;

    .skeleton-metadata {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }

    .skeleton-meta-item {
      display: flex;
      align-items: center;
      gap: 6px;
    }

    .skeleton-actions {
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }
}

// Skeleton element base styles
.skeleton-text,
.skeleton-icon,
.skeleton-icon-small,
.skeleton-button {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: 4px;
}

// Skeleton text variations
.skeleton-text {
  height: 16px;

  &.skeleton-category-title {
    width: 60px;
    height: 12px;
  }

  &.skeleton-number {
    width: 20px;
    height: 12px;
  }

  &.skeleton-title {
    width: 85%;
    height: 24px;
    border-radius: 6px;
  }

  &.skeleton-description-1 {
    width: 100%;
    height: 14px;
    margin-bottom: 8px;
  }

  &.skeleton-description-2 {
    width: 90%;
    height: 14px;
    margin-bottom: 8px;
  }

  &.skeleton-description-3 {
    width: 75%;
    height: 14px;
  }

  &.skeleton-author {
    width: 70px;
    height: 12px;
  }

  &.skeleton-date {
    width: 55px;
    height: 12px;
  }
}

// Skeleton icon styles
.skeleton-icon {
  width: 18px;
  height: 18px;
  border-radius: 50%;
}

.skeleton-icon-small {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

// Skeleton button styles
.skeleton-button {
  width: 32px;
  height: 32px;
  border-radius: 8px;

  &.skeleton-button-primary {
    background: linear-gradient(
      90deg,
      #2f5a8e40 25%,
      #1a46a740 50%,
      #2f5a8e40 75%
    );
    background-size: 200% 100%;
  }
}

// Shimmer animation for skeleton
@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }

  100% {
    background-position: 200% 0;
  }
}

.disabled {
  cursor: not-allowed;
  pointer-events: none;
  color: var(--Text-Disabled, #9ca3af);
}

::ng-deep .stat-icon-container svg {
  stroke: #ed6999;
}

// Tooltip styling for truncated text
[data-tooltip] {
  position: relative;
  cursor: help;
}

[data-tooltip]:hover::after {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 12px;
  white-space: normal;
  max-width: 250px;
  word-wrap: break-word;
  z-index: 1000;
  pointer-events: none;
  opacity: 0;
  animation: tooltip-fade-in 0.2s ease-in-out forwards;
}

[data-tooltip]:hover::before {
  content: "";
  position: absolute;
  bottom: calc(100% - 6px);
  left: 50%;
  transform: translateX(-50%);
  border: 6px solid transparent;
  border-top-color: rgba(0, 0, 0, 0.9);
  z-index: 1000;
  pointer-events: none;
  opacity: 0;
  animation: tooltip-fade-in 0.2s ease-in-out forwards;
}

@keyframes tooltip-fade-in {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(4px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

.profile-trigger {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
  padding: 2px;

  .profile-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid rgba(255, 255, 255, 0.2);
  }
}
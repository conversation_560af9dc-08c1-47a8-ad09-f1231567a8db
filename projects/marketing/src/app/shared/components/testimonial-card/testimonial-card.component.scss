.testimonial-card {
  display: flex;
  flex-direction: column;
  background: #ffffff;
  border-radius: 16px;
  padding: 32px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  max-width: 400px;
  margin: 0 auto;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
  overflow: hidden;
  gap: 16px;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  }
}

// Header Styles
.testimonial-header {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

// Avatar Styles
.testimonial-avatar {
  .avatar-image {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    object-fit: cover;
    border: 4px solid #f8f9fa;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;

    &:hover {
      transform: scale(1.05);
    }
  }
}

// Quote Styles
.testimonial-quote {
  flex: 1;
  position: relative;

  .quote-mark {
    font-size: 2rem;
    font-weight: 700;
    color: #667eea;
    opacity: 0.7;
    font-family: 'Georgia', serif;

    &.opening {
      margin-right: 4px;
    }

    &.closing {
      margin-left: 4px;
    }
  }

  .quote-text {
    color: var(--Colors-Text-secondary, #616874);
    font-family: Mulish;
    font-size: 21px;
    font-style: normal;
    font-weight: 400;
    line-height: 32px; /* 152.381% */
    letter-spacing: -0.5px;
    margin: 0;
    position: relative;
    z-index: 1;
  }
}



// Footer Styles
.testimonial-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .author-info {
    flex: 1 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .author-name {
      color: var(--Colors-Text-secondary, #616874);
      font-family: Mulish;
      font-size: 17px;
      font-style: normal;
      font-weight: 700;
      line-height: 29px; /* 170.588% */
      letter-spacing: -0.2px;
      margin: 0;
    }

    .author-role {
      color: #161C2D;
      font-family: Mulish;
      font-size: 17px;
      font-style: normal;
      font-weight: 400;
      line-height: 29px; /* 170.588% */
      letter-spacing: -0.2px;
      margin: 0;

      .company {
        color: #161C2D;
        font-weight: 400;
      }
    }
  }
}

// Variant Styles
.testimonial-compact {
  padding: 20px;
  max-width: 300px;

  .testimonial-avatar {    
    .avatar-image {
      width: 60px;
      height: 60px;
    }
  }

  .testimonial-quote {
    margin-bottom: 20px;
  }

  }

.testimonial-detailed {
  padding: 40px;
  max-width: 480px;

  .testimonial-avatar .avatar-image {
    width: 100px;
    height: 100px;
  }

  .testimonial-quote .quote-text {
    font-size: 18px;
  }

  .testimonial-footer .author-info {
    .author-name {
      font-size: 20px;
    }

    .author-role {
      font-size: 15px;
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .testimonial-card {
    padding: 24px;
    max-width: 100%;
    margin: 0 16px;

    .testimonial-avatar .avatar-image {
      width: 70px;
      height: 70px;
    }

    .testimonial-quote .quote-text {
      font-size: 15px;
    }

    .testimonial-footer .author-info {
      .author-name {
        font-size: 16px;
      }

      .author-role {
        font-size: 13px;
      }
    }
  }

  .testimonial-detailed {
    padding: 32px;

    .testimonial-avatar .avatar-image {
      width: 80px;
      height: 80px;
    }
  }
}

@media (max-width: 480px) {
  .testimonial-card {
    padding: 20px;

    .testimonial-avatar .avatar-image {
      width: 60px;
      height: 60px;
    }

    .testimonial-quote {
      .quote-mark {
        font-size: 1.5rem;
      }

      .quote-text {
        font-size: 14px;
      }
    }

    .testimonial-footer .author-info {
      .author-name {
        font-size: 15px;
      }

      .author-role {
        font-size: 12px;
      }
    }
  }
}

// Dark theme support
@media (prefers-color-scheme: dark) {
  .testimonial-card {
    // background: #1a202c;
    // color: #e2e8f0;

    .testimonial-quote .quote-text {
      // color: #cbd5e0;
    }

    .testimonial-author .author-info {
      .author-name {
        // color: #f7fafc;
      }

      .author-role {
        color: #a0aec0;
      }
    }
  }
}

// Accessibility
@media (prefers-reduced-motion: reduce) {
  .testimonial-card,
  .testimonial-avatar .avatar-image,
  .testimonial-rating .star {
    transition: none;
  }

  .testimonial-card:hover {
    transform: none;
  }

  .testimonial-avatar .avatar-image:hover {
    transform: none;
  }
}

// High contrast mode
@media (prefers-contrast: high) {
  .testimonial-card {
    border: 2px solid #000;
    box-shadow: none;

    .testimonial-quote .quote-mark {
      color: #000;
      opacity: 1;
    }

    .testimonial-footer .author-info .author-role .company {
      color: #000;
      text-decoration: underline;
    }
  }
}
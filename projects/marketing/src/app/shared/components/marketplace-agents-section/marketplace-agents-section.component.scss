.marketplace-agents-section {
  padding: 40px 0;
  background: #ffffff;

  .container {
    width: 100%;
    padding: 0 32px;
  }

  .section-header {
    text-align: center;
    margin-bottom: 16px;

    .section-title {
      // Layout styles
      color: #3b3f46;
      text-align: center;
      font-family: Mulish;
      font-size: 48px;
      font-style: normal;
      font-weight: 700;
      line-height: normal;
      letter-spacing: -0.912px;

      .highlight {
        // Gradient styles for "Preview"
        background: linear-gradient(90deg, #6566cd 36.04%, #f96cab 110.55%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        font-family: Mulish;
        font-size: 48px;
        font-style: normal;
        font-weight: 700;
        line-height: normal;
        letter-spacing: -0.912px;
      }

      @media (max-width: 768px) {
        font-size: 36px;
        letter-spacing: -0.684px;

        .highlight {
          font-size: 36px;
          letter-spacing: -0.684px;
        }
      }

      @media (max-width: 480px) {
        font-size: 28px;
        letter-spacing: -0.532px;

        .highlight {
          font-size: 28px;
          letter-spacing: -0.532px;
        }
      }
    }

    .section-subtitle {
      // Layout styles
      color: #161c2d;
      font-family: Mulish;
      font-size: 24px;
      font-style: normal;
      font-weight: 500;
      line-height: 32px; /* 133.333% */
      letter-spacing: -0.2px;
      margin: 0;
      // Opacity styles
      opacity: 0.7;
      margin-bottom: 40px;

      @media (max-width: 768px) {
        font-size: 20px;
        line-height: 28px;
        letter-spacing: -0.167px;
      }

      @media (max-width: 480px) {
        font-size: 18px;
        line-height: 24px;
        letter-spacing: -0.15px;
      }
    }
  }

  .agents-grid {
    overflow-x: auto; // Allow scrolling
    scroll-behavior: smooth;
    padding-bottom: 20px;
    position: relative;
    width: 100%;

    // Hide scrollbar completely but keep scroll functionality
    scrollbar-width: none; // Firefox
    -ms-overflow-style: none; // Internet Explorer 10+

    // Hide scrollbar for webkit browsers
    &::-webkit-scrollbar {
      display: none;
    }

    // Always hide scrollbar for users who prefer reduced motion
    @media (prefers-reduced-motion: reduce) {
      overflow-x: auto;
      scrollbar-width: none;
      -ms-overflow-style: none;

      &::-webkit-scrollbar {
        display: none;
      }
    }
  }

  .agents-track {
    display: flex;
    gap: 40px; // 40px gap between each section as requested
    width: max-content;

    // Auto-scroll animation for marketplace
    animation: autoScroll 30s linear infinite;

    // Pause animation on hover for better user experience
    &:hover {
      animation-play-state: paused;
    }

    @media (max-width: 768px) {
      gap: 32px; // Slightly smaller gap on mobile
      animation: autoScroll 25s linear infinite; // Slightly faster on mobile
    }

    @media (max-width: 480px) {
      gap: 24px; // Even smaller gap on very small screens
      animation: autoScroll 20s linear infinite; // Even faster on small screens
    }

    // Respect user's motion preferences
    @media (prefers-reduced-motion: reduce) {
      animation: none;
    }
  }
}

// Auto-scroll animation keyframes for marketplace
@keyframes autoScroll {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(
      -50%
    ); // Scroll by half the content width to create seamless loop
  }
}

// Override agent card styles for this section
.marketplace-agents-section {
  ::ng-deep app-marketplace-agent-card {
    .marketplace-agent-card {
      background: #ffffff; // White background as requested
      border: 1px solid #e5e7eb;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
      border-radius: 16px;
      transition:
        transform 0.2s ease,
        box-shadow 0.2s ease;
      height: 100%;
      width: 561px; // Exact width as specified
      flex-shrink: 0;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
      }

      @media (max-width: 768px) {
        width: 400px; // Responsive width for tablets
      }

      @media (max-width: 480px) {
        width: 320px; // Responsive width for mobile
      }
    }
  }
}

// Dark theme support - keep default colors and background (no overrides needed)

// Responsive container adjustments
@media (max-width: 1200px) {
  .marketplace-agents-section {
    .container {
      padding: 0 24px;
    }
  }
}

@media (max-width: 768px) {
  .marketplace-agents-section {
    padding: 60px 0;

    .container {
      padding: 0 16px;
    }

    .section-header {
      margin-bottom: 48px;
    }
  }
}

@media (max-width: 480px) {
  .marketplace-agents-section {
    padding: 40px 0;

    .section-header {
      margin-bottom: 32px;
    }

    .agents-grid {
      gap: 12px;
    }
  }
}

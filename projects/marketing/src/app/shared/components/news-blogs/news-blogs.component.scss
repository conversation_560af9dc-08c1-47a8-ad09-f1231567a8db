.news-blogs {
  padding: 80px 0;
  background: #ffffff;

  .container {
    width: 100%;
    padding: 0 32px;
  }

  .header {
    text-align: center;
    margin-bottom: 48px;

    h2 {
      color: #14161f;
      font-family: 'Mulish', sans-serif;
      font-size: 48px;
      font-weight: 700;
      line-height: 1.2;
      letter-spacing: -0.5px;
      margin: 0;

      .gradient-text {
        background: linear-gradient(90deg, #6566CD 36.04%, #F96CAB 118.04%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        color: transparent;
      }
    }
  }

  .blog-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: repeat(2, 1fr);
    gap: 24px;
    width: 100%;
  }

  .blog-card {
    display: flex;
    align-items: stretch;
    background: #ffffff;
    border: 2px solid;
    border-image-source: linear-gradient(180deg, rgba(255, 255, 255, 0.9) 0%, #FFFFFF 100%);
    border-radius: 24px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    min-height: 200px;

    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
    }
  }

  .blog-image {
    flex: 0 0 200px;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;

    img {
      width: 228px;
      height: 228px;
      object-fit: fill;
      border-radius: 16px;
      padding: 16px;
    }
  }

  .blog-content {
    flex: 1;
    padding: 24px;
    display: flex;
    flex-direction: column;
    min-height: 0; // Allow flex items to shrink
  }

  .blog-main-content {
    flex: 1; // Take up available space
    display: flex;
    flex-direction: column;
  }

  .blog-bottom-section {
    margin-top: auto; // Push to bottom
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .blog-content h3 {
    color: #292c3d;
    font-family: 'Mulish', sans-serif;
    font-size: 20px;
    font-weight: 700;
    line-height: 1.3;
    margin: 0 0 12px 0;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }

  .blog-content .description {
    color: #666d99;
    font-family: 'Mulish', sans-serif;
    font-size: 14px;
    font-weight: 400;
    line-height: 1.5;
    margin: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
  }

  .read-more {
    background: linear-gradient(90.1deg, #F96CAB -14.75%, #4244C2 34.27%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    font-family: 'Mulish', sans-serif;
    font-size: 16px;
    font-weight: 500;
    text-decoration: none;
    display: inline-block;
    transition: color 0.3s ease;
    line-height: 140%;
    letter-spacing: 0.5%;

    &:hover {
      color: #7c3aed;
      text-decoration: underline;
    }
  }

  .divider {
    border: none;
    border-top: 1px solid #292C3D;
    width: 100%;
    height: 0;
    opacity: 0.2;
    margin: 0;
  }

  .blog-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .author {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .author-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    object-fit: cover;
  }

  .author-name {
    color: #292c3d;
    font-family: 'Mulish', sans-serif;
    font-size: 12px;
    font-weight: 500;
    line-height: 1.4;
  }

  .views {
    display: flex;
    align-items: center;
    gap: 4px;
    color: #666d99;
    font-family: 'Mulish', sans-serif;
    font-size: 12px;
    font-weight: 500;
  }
}

// Responsive Design
@media (max-width: 1024px) {
  .news-blogs {
    .container {
      padding: 0 24px;
    }

    .blog-grid {
      gap: 20px;
    }

    .blog-card {
      min-height: 180px;
    }

    .blog-image {
      flex: 0 0 160px;
      width: 160px;
    }

    .blog-content {
      padding: 20px;
    }

    .blog-content h3 {
      font-size: 18px;
    }
  }
}

@media (max-width: 768px) {
  .news-blogs {
    padding: 60px 0;

    .container {
      padding: 0 20px;
    }

    .header h2 {
      font-size: 36px;
    }

    .blog-grid {
      grid-template-columns: 1fr;
      grid-template-rows: auto;
      gap: 16px;
    }

    .blog-card {
      flex-direction: column;
      min-height: auto;
    }

    .blog-image {
      flex: none;
      width: 100%;
      height: 200px;
    }

    .blog-content {
      padding: 20px;
    }

    .blog-content h3 {
      font-size: 18px;
      margin-bottom: 8px;
    }

    .blog-content .description {
      font-size: 14px;
      -webkit-line-clamp: 2;
    }

    .blog-bottom-section {
      gap: 12px;
    }
  }
}

@media (max-width: 480px) {
  .news-blogs {
    padding: 40px 0;

    .container {
      padding: 0 16px;
    }

    .header h2 {
      font-size: 28px;
    }

    .blog-grid {
      gap: 12px;
    }

    .blog-image {
      height: 160px;
    }

    .blog-content {
      padding: 16px;
    }

    .blog-content h3 {
      font-size: 16px;
    }

    .blog-content .description {
      font-size: 13px;
    }
  }
}
 
# Client Card Component

A reusable client card component for displaying client testimonials with company branding, perfect for showcasing customer success stories and building trust.

## Features

- 🏢 **Company Branding**: Display company logos prominently
- 💬 **Client Testimonials**: Featured customer quotes and feedback
- 👤 **Author Attribution**: Client contact with avatar and role
- 🎨 **Multiple Variants**: Default, compact, and featured layouts
- 📱 **Responsive Design**: Mobile-first approach with breakpoints
- 🌙 **Dark Theme Support**: Automatic dark mode adaptation
- ♿ **Accessibility**: Screen reader friendly with proper ARIA labels

## Basic Usage

### Using Client Object (Recommended)

```typescript
import { ClientCardComponent, Client } from '@shared/components/client-card/client-card.component';

export class MyComponent {
  client: Client = {
    id: 1,
    companyName: 'CVS Health',
    companyLogo: 'assets/logos/cvs-health.svg',
    testimonial: 'Uses Growthly as the source of truth for all its product data, and to determine where the team should focus its time.',
    authorName: 'Cyhn<PERSON><PERSON>',
    authorRole: 'Dev at CVS Health',
    authorAvatar: 'assets/avatars/cyhntya.jpg'
  };
}
```

```html
<app-client-card [client]="client"></app-client-card>
```

### Using Individual Inputs

```html
<app-client-card
  companyName="CVS Health"
  companyLogo="assets/logos/cvs-health.svg"
  testimonial="Amazing platform! Highly recommended."
  authorName="John Doe"
  authorRole="Senior Developer at CVS Health"
  authorAvatar="assets/avatars/john.jpg">
</app-client-card>
```

## Component Inputs

### Core Properties

| Input | Type | Default | Description |
|-------|------|---------|-------------|
| `client` | `Client` | - | Complete client object (recommended) |
| `companyName` | `string` | `''` | Company/organization name |
| `companyLogo` | `string` | `''` | Company logo image URL |
| `testimonial` | `string` | `''` | Client testimonial/quote text |
| `authorName` | `string` | `''` | Client contact name |
| `authorRole` | `string` | `''` | Client's job title and company |
| `authorAvatar` | `string` | `''` | Client avatar image URL |

### Customization Options

| Input | Type | Default | Description |
|-------|------|---------|-------------|
| `variant` | `'default' \| 'compact' \| 'featured'` | `'default'` | Card size variant |
| `backgroundColor` | `string` | `'#ffffff'` | Card background color |
| `textColor` | `string` | `'#333333'` | Primary text color |
| `borderRadius` | `string` | `'12px'` | Card border radius |
| `padding` | `string` | `'24px'` | Internal padding |
| `shadow` | `string` | `'0 4px 16px rgba(0, 0, 0, 0.08)'` | Box shadow |
| `maxWidth` | `string` | `'320px'` | Maximum card width |

## Client Interface

```typescript
interface Client {
  id?: string | number;
  companyName: string;
  companyLogo: string;
  testimonial: string;
  authorName: string;
  authorRole: string;
  authorAvatar: string;
  backgroundColor?: string;
}
```

## Variants

### Default
Standard size with balanced proportions.

```html
<app-client-card [client]="client" variant="default"></app-client-card>
```

### Compact
Smaller size for tight layouts or mobile views.

```html
<app-client-card [client]="client" variant="compact"></app-client-card>
```

### Featured
Larger size for highlighting key testimonials.

```html
<app-client-card [client]="client" variant="featured"></app-client-card>
```

## Custom Styling

```html
<app-client-card
  [client]="client"
  backgroundColor="#f8f9fa"
  textColor="#2d3748"
  borderRadius="16px"
  padding="32px"
  shadow="0 8px 32px rgba(0, 0, 0, 0.12)"
  maxWidth="400px">
</app-client-card>
```

## Grid Layout Example

```html
<div class="clients-grid">
  <app-client-card
    *ngFor="let client of clients; trackBy: trackByClient"
    [client]="client">
  </app-client-card>
</div>
```

```scss
.clients-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}
```

## Related Components

- `ClientsSectionComponent` - Section wrapper for multiple client cards
- `TestimonialCardComponent` - For individual user testimonials
- `HeroBannerComponent` - For hero sections with client logos 
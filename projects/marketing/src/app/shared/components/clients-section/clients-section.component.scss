.clients-section {
  padding: 40px 0;
  background: #ffffff;
  margin-bottom: 40px;

  .container {
    width: 100%;
    padding: 0 32px;
  }

  .section-header {
    text-align: center;
    margin-bottom: 24px;

    .section-title {
      // Layout styles - same as marketplace header
      color: #3B3F46;
      text-align: center;
      font-family: Mulish;
      font-size: 48px;
      font-style: normal;
      font-weight: 700;
      line-height: normal;
      letter-spacing: -0.912px;
      margin: 0 0 16px 0;

      .highlight {
        // Gradient styles for "Clients" - same as marketplace
        background: linear-gradient(90deg, #6566CD 36.04%, #F96CAB 110.55%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        font-family: Mulish;
        font-size: 48px;
        font-style: normal;
        font-weight: 700;
        line-height: normal;
        letter-spacing: -0.912px;
      }

      @media (max-width: 768px) {
        font-size: 36px;
        letter-spacing: -0.684px;

        .highlight {
          font-size: 36px;
          letter-spacing: -0.684px;
        }
      }

      @media (max-width: 480px) {
        font-size: 28px;
        letter-spacing: -0.532px;

        .highlight {
          font-size: 28px;
          letter-spacing: -0.532px;
        }
      }
    }
  }

  .clients-grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 40px; // 40px gaps between major sections as per user preference
    align-items: stretch;
    width: 100%;
    margin: 0 auto;
    overflow-y: auto;
    justify-items: center; // Center the 280px cards

    @media (max-width: 1200px) {
      grid-template-columns: repeat(3, 1fr);
      gap: 32px;
    }

    @media (max-width: 768px) {
      grid-template-columns: repeat(2, 1fr);
      gap: 24px;
    }

    @media (max-width: 480px) {
      grid-template-columns: 1fr;
      gap: 16px;
    }
  }
}

// Override client card styles for this section
.clients-section {
  ::ng-deep app-client-card {
    .client-card {
      background: #ffffff;
      border: 1px solid #e2e8f0;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
      border-radius: 12px;
      padding: 24px;
      height: 100%;
      margin: 0;
      transition: transform 0.2s ease, box-shadow 0.2s ease;
      display: flex;
      flex-direction: column;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
      }
    }

    .client-logo {
      margin-bottom: 16px;

      .logo-image {
        height: 32px;
        max-width: 120px;
      }
    }

    .client-testimonial {
      margin-bottom: 16px; // 16px gap between header content and footer
      flex: 1;

      .testimonial-text {
        color: var(--Greyscale-Grey, #5D6180);
        font-family: Mulish;
        font-size: 16px;
        font-style: italic;
        font-weight: 400;
        line-height: 150%; /* 24px */
        margin: 0;
      }
    }

    .client-author {
      gap: 12px;

      .author-avatar .avatar-image {
        width: 40px;
        height: 40px;
        border: 2px solid #e2e8f0;
      }

      .author-info {
        .author-name {
          // Footer header styling
          color: var(--Greyscale-Dark-Grey, #060C3C);
          font-variant-numeric: ordinal;
          font-family: Mulish;
          font-size: 18px;
          font-style: normal;
          font-weight: 700;
          line-height: 24px; /* 133.333% */
          margin-bottom: 2px;
        }

        .author-role {
          // Footer content styling
          color: var(--Purple-Purple, #6241D4);
          font-family: Mulish;
          font-size: 16px;
          font-style: normal;
          font-weight: 400;
          line-height: 26px; /* 162.5% */
        }
      }
    }
  }
}

// // Dark theme support
// @media (prefers-color-scheme: dark) {
//   .clients-section {

//     .section-header {
//       .section-title {
//       }
//     }

//     ::ng-deep app-client-card {
//       .client-card {
//       }

//       .client-testimonial .testimonial-text {
//       }

//       .client-author {
//         .author-avatar .avatar-image {
//         }

//         .author-info {
//           .author-name {
//           }

//           .author-role {
//           }
//         }
//       }
//     }
//   }
// } 
<svg width="33" height="40" viewBox="0 0 33 40" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_i_7731_9187)">
<rect x="16.2529" width="22.9827" height="22.9827" rx="3.06436" transform="rotate(45 16.2529 0)" fill="url(#paint0_linear_7731_9187)"/>
</g>
<g filter="url(#filter1_i_7731_9187)">
<rect x="16.251" y="40" width="22.9827" height="22.9827" rx="3.06436" transform="rotate(-135 16.251 40)" fill="url(#paint1_linear_7731_9187)"/>
</g>
<defs>
<filter id="filter0_i_7731_9187" x="0.00195312" y="-1.53218" width="32.502" height="34.0346" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-1.53218"/>
<feGaussianBlur stdDeviation="1.45557"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.66 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_7731_9187"/>
</filter>
<filter id="filter1_i_7731_9187" x="0" y="5.96541" width="32.502" height="34.0346" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-1.53218"/>
<feGaussianBlur stdDeviation="1.37896"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_7731_9187"/>
</filter>
<linearGradient id="paint0_linear_7731_9187" x1="16.0073" y1="0.175618" x2="34.6807" y2="18.1804" gradientUnits="userSpaceOnUse">
<stop stop-color="#6566CD"/>
<stop offset="0.966726" stop-color="#F96CAB"/>
</linearGradient>
<linearGradient id="paint1_linear_7731_9187" x1="16.6863" y1="40.6438" x2="30.457" y2="53.9707" gradientUnits="userSpaceOnUse">
<stop stop-color="#6566CD"/>
<stop offset="1" stop-color="#F96CAB"/>
</linearGradient>
</defs>
</svg>

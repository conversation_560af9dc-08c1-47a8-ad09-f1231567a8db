import { CommonModule } from '@angular/common';
import { Component, OnInit, OnDestroy } from '@angular/core';
import { HeadingComponent, BodyTextComponent, CardsComponent } from '@awe/play-comp-library';
import { Router } from '@angular/router';
import { takeUntil } from 'rxjs/operators';
import { Subject } from 'rxjs';
import { GetRecentProjectsService, ProjectListItem } from '../../../brainstormer/services/get-recent-projects.service';
import { SummaryService } from '../../../brainstormer/components/summary/summary.service';
import { AweModalComponent } from '../../../brainstormer/components/awe-modal/awe-modal.component';

interface CardOption {
  id: string;
  heading: string;
  description: string;
  actionText?: string;
  type: string;
  timestamp?: string;
}

@Component({
  selector: 'app-recent-projects',
  standalone: true,
  imports: [CommonModule, HeadingComponent, BodyTextComponent, CardsComponent, AweModalComponent],
  templateUrl: './recent-projects.component.html',
  styleUrl: './recent-projects.component.scss'
})
export class RecentProjectsComponent implements OnInit, OnDestroy {
  theme: 'light' | 'dark' = 'light';
  selectedId: string | null = null;
  currentCategory: string = 'recent';
  isLoading: boolean = true;
  error: string | null = null;
  projects: ProjectListItem[] = [];
  options: { [category: string]: CardOption[] } = {
    recent: [],
    all: []
  };

  // Loading states for individual cards
  loadingCardId: string | null = null;
  isCardLoading: boolean = false;

  // Modal properties
  isErrorModalOpen = false;
  errorModalTitle = 'Error';
  errorModalMessage = '';
  errorModalType: 'error' | 'success' | 'info' | 'warning' = 'error';

  private destroy$ = new Subject<void>();

  constructor(
    private getRecentProjectsService: GetRecentProjectsService,
    private router: Router,
    private summaryService: SummaryService
  ) {}

  ngOnInit() {

    // Initialize with skeleton placeholders
    this.initializePlaceholders();

    // Load projects automatically when component initializes
    this.loadProjectsFromAPI();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Load projects from API using GetRecentProjectsService
   */
  public loadProjectsFromAPI(): void {
    this.isLoading = true;
    this.error = null;


    this.getRecentProjectsService.getRecentProjects()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (projects) => {
          this.projects = projects;
          this.processProjectsData();
          this.isLoading = false;
        },
        error: (error) => {
          console.error('❌ RecentProjectsComponent: Failed to load projects from API:', error);
          this.error = 'Failed to load recent projects. Please try again.';
          this.isLoading = false;
          // Fallback to empty state
          this.projects = [];
          this.processProjectsData();
        }
      });
  }

  /**
   * Process loaded projects data and update options
   */
  private processProjectsData(): void {
    if (this.projects.length === 0) {
      this.options['recent'] = [];
      this.options['all'] = [];
      return;
    }

    // Convert API projects to CardOptions format
    const cardOptions = this.mapAPIProjectsToCardOptions(this.projects);

    // Load recent projects (first 4 items)
    this.options['recent'] = cardOptions.slice(0, 4);

    // Load all projects
    this.options['all'] = cardOptions;

  }

  private initializePlaceholders() {
    // Initialize with skeleton placeholders
    this.options['recent'] = Array(4).fill(null).map((_, i) => ({
      id: `placeholder-${i}`,
      heading: '',
      description: '',
      type: '',
      timestamp: ''
    }));

    this.options['all'] = Array(12).fill(null).map((_, i) => ({
      id: `placeholder-${i}`,
      heading: '',
      description: '',
      type: '',
      timestamp: ''
    }));
  }

  /**
   * Map API projects (ProjectListItem[]) to CardOption[] format
   */
  private mapAPIProjectsToCardOptions(projects: ProjectListItem[]): CardOption[] {
    return projects.map(project => ({
      id: project.id,
      heading: project.name,
      description: this.truncateDescription(project.description),
      type: this.determineProjectType(project),
      timestamp: this.formatAPIDate(project.updated_at)
    }));
  }

  /**
   * Determine project type from API project data
   * Since API doesn't provide project_type, we'll use a default or derive from tags
   */
  private determineProjectType(project: ProjectListItem): string {
    // Check if project has tags that might indicate type
    if (project.tags && project.tags.length > 0) {
      const tag = project.tags[0].toLowerCase();
      if (['ui', 'app', 'analysis', 'accessibility'].includes(tag)) {
        return tag;
      }
    }

    // Default to 'app' if no specific type can be determined
    return 'app';
  }

  /**
   * Format API date string for display
   */
  private formatAPIDate(dateString: string): string {
    try {
      const date = new Date(dateString);
      return `${date.getDate()} ${date.toLocaleString('default', { month: 'short' })}`;
    } catch {
      return 'Unknown';
    }
  }

  private truncateDescription(description: string): string {
    const words = description.split(' ');
    // Limit to approximately 3 lines (about 10-12 words)
    if (words.length > 10) {
      return words.slice(0, 20).join(' ') + '...';
    }
    return description;
  }

  switchCategory(category: string): void {
    if (this.currentCategory !== category) {
      // Update category immediately
      this.currentCategory = category;

      // Use requestAnimationFrame for smoother animation
      requestAnimationFrame(() => {
        const gridElement = document.querySelector('.cards-grid') as HTMLElement;
        if (gridElement) {
          // Remove existing animation classes
          gridElement.classList.remove('slide-recent', 'slide-all');

          // Force a reflow to ensure the animation triggers
          void gridElement.offsetWidth;

          // Add the appropriate animation class
          gridElement.classList.add(category === 'recent' ? 'slide-recent' : 'slide-all');
        }
      });
    }
  }

  getCurrentOptions(): CardOption[] {
    return this.options[this.currentCategory] || [];
  }

  isSelected(id: string): boolean {
    return this.selectedId === id;
  }

  handleSelection(id: string, event?: Event): void {
    if (event) {
      event.preventDefault();
    }


    // Prevent selection if already loading a card
    if (this.isCardLoading) {
      return;
    }

    this.selectedId = id;
    this.loadingCardId = id;
    this.isCardLoading = true;

    // Loading state updated

    // Find the selected project and load it
    const selectedProject = this.projects.find(project => project.id === id);
    if (selectedProject) {
      this.loadSelectedProject(selectedProject);
    } else {
      // Could not find project with ID
      // Reset loading states if project not found
      this.loadingCardId = null;
      this.isCardLoading = false;
      this.selectedId = null;
    }
  }

  /**
   * Load selected project and navigate to brainstorming interface
   */
  private loadSelectedProject(project: ProjectListItem): void {

    this.getRecentProjectsService.loadProject(project.run_id)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (projectDetails) => {
          // Force refresh summary data after project is loaded
          // this.summaryService.forceDataRefresh();

          // Add a small delay to ensure state is updated before navigation
          setTimeout(() => {
            this.router.navigate(['/brainstormer/brainstorming']);
            // Note: Loading states will be reset when component is destroyed during navigation
          }, 500);
        },
        error: (error) => {
          // Failed to load project

          // Reset loading states on error
          this.loadingCardId = null;
          this.isCardLoading = false;
          this.selectedId = null;

          // Could show a toast or error message to user
          this.showErrorModal('Load Failed', `Failed to load project "${project.name}". Please try again.`, 'error');
        }
      });
  }

  getDefaultActionText(type: string): string {
    const actionMap: { [key: string]: string } = {
      'ui': 'Generate UI',
      'app': 'Generate App',
      'analysis': 'Design Analysis',
      'accessibility': 'Review Accessibility'
    };
    return actionMap[type.toLowerCase()] || 'View';
  }

  trackByFn(_: number, item: CardOption): string {
    return item.id;
  }

  /**
   * Check if a specific card is currently loading
   */
  isCardLoadingState(cardId: string): boolean {
    const isLoading = this.loadingCardId === cardId && this.isCardLoading;
    if (isLoading) {
    }
    return isLoading;
  }

  /**
   * Check if any card is loading (for overlay effect)
   */
  isAnyCardLoading(): boolean {
    return this.isCardLoading;
  }

  /**
   * Check if a card should be disabled (not the loading card but another card is loading)
   */
  isCardDisabled(cardId: string): boolean {
    const isDisabled = this.isCardLoading && this.loadingCardId !== cardId;
    if (isDisabled) {
    }
    return isDisabled;
  }

  // Modal methods
  showErrorModal(title: string, message: string, type: 'error' | 'success' | 'info' | 'warning' = 'error'): void {
    this.errorModalTitle = title;
    this.errorModalMessage = message;
    this.errorModalType = type;
    this.isErrorModalOpen = true;
  }

  closeErrorModal(): void {
    this.isErrorModalOpen = false;
  }

  getModalClass(): string {
    switch (this.errorModalType) {
      case 'error':
        return 'modal-error';
      case 'success':
        return 'modal-success';
      case 'warning':
        return 'modal-warning';
      case 'info':
      default:
        return 'modal-info';
    }
  }
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  backdrop-filter: blur(4px);
  animation: fadeIn 0.2s ease-out;
}

.modal-container {
  background: var(--modal-background, #ffffff);
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  max-width: 480px;
  width: 90%;
  max-height: 90vh;
  overflow: hidden;
  animation: slideIn 0.3s ease-out;
  border: 1px solid var(--modal-border, rgba(0, 0, 0, 0.1));
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px 16px;
  border-bottom: 1px solid var(--modal-divider, rgba(0, 0, 0, 0.1));
  
  .modal-title {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    color: var(--text-primary-color, #333333);
    line-height: 1.3;
  }
  
  .close-button {
    background: none;
    border: none;
    padding: 8px;
    cursor: pointer;
    border-radius: 6px;
    transition: background-color 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    
    &:hover {
      background-color: var(--button-hover-background, rgba(0, 0, 0, 0.05));
    }
    
    &:focus {
      outline: 2px solid var(--focus-color, #007bff);
      outline-offset: 2px;
    }
  }
}

.modal-body {
  padding: 24px;
  text-align: center;
  
  .warning-icon {
    margin-bottom: 16px;
    display: flex;
    justify-content: center;
    
    awe-icons {
      color: var(--warning-color, #ff9800);
    }
  }
  
  .modal-message {
    margin: 0;
    font-size: 16px;
    line-height: 1.5;
    color: var(--text-secondary-color, #666666);
    font-weight: 400;
  }
}

.modal-footer {
  display: flex;
  gap: 12px;
  padding: 20px 24px 24px;
  justify-content: flex-end;
  border-top: 1px solid var(--modal-divider, rgba(0, 0, 0, 0.1));
  
  .cancel-button,
  .confirm-button {
    min-width: 80px;
  }
  
  .confirm-button {
    background-color: var(--danger-color, #dc3545);
    
    &:hover {
      background-color: var(--danger-hover-color, #c82333);
    }
  }
}

// Animations
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

// Dark theme support
:host-context(.theme-dark) {
  .modal-container {
    --modal-background: #2d2d2d;
    --modal-border: rgba(255, 255, 255, 0.1);
    --modal-divider: rgba(255, 255, 255, 0.1);
    --text-primary-color: #ffffff;
    --text-secondary-color: #cccccc;
    --button-hover-background: rgba(255, 255, 255, 0.1);
  }
}

// Responsive design
@media (max-width: 768px) {
  .modal-container {
    margin: 16px;
    width: calc(100% - 32px);
  }
  
  .modal-footer {
    flex-direction: column-reverse;
    
    .cancel-button,
    .confirm-button {
      width: 100%;
    }
  }
}

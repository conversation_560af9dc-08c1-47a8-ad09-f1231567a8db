import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Inject, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Observable, Subscription } from 'rxjs';
import { map } from 'rxjs/operators';
import {
  trigger,
  state,
  style,
  transition,
  animate,
} from '@angular/animations';

import {
  LeftPanelComponent,
  RightPanelComponent,
  SplitScreenComponent as AweSplitScreenComponent
} from '@awe/play-comp-library';
import {
  StepperService,
  StepperStep,
} from '../../shared/services/stepper-service/stepper.service';

// Import all page components

import { UserPersonaComponent } from '../pages/user-persona/user-persona.component';
import { PersonaDetailsComponent } from '../pages/persona-details/persona-details.component';
import { FeatureListComponent } from '../pages/feature-list/feature-list.component';
import { SwotAnalysisComponent } from '../pages/swot-analysis/swot-analysis.component';
import { ProductRoadmapComponent } from '../pages/product-roadmap/product-roadmap.component';
// Remove the custom split-screen component import as we're using AVA play library
import { ChatPanelComponent } from '../components/chat-panel/chat-panel.component';
import { LeftPanelHeaderComponent } from '../components/left-panel-header/left-panel-header.component';
import { NewRightPanelHeaderComponent } from '../components/new-right-panel-header/new-right-panel-header.component';
import { UnderstandingComponent } from '../pages/understanding/understanding.component';
import { Router } from '@angular/router';
import { ProductPipelineService } from '../services/product-pipeline.service';
// LoadingComponent removed - loading states now handled by stepper
import { AppStateService } from '../../shared/services/app-state.service';
import { NavigationGuardService } from '../../shared/services/navigation-guard.service';
import { NavigationConfirmationModalComponent } from '../../shared/components/navigation-confirmation-modal/navigation-confirmation-modal.component';
import { PanelToggleService } from '../services/panel-toggle.service';
import { ThemeServiceService } from '../../shared/services/auth-config-service/theme-service.service';
import { SummaryService } from '../components/summary/summary.service';
import { SummaryComponent } from '../components/summary/summary.component';
import { ProjectDetailsState } from '../interfaces/pipeline-api.interface';
// import { DebugStateComponent } from '../components/debug-state/debug-state.component';

@Component({
  selector: 'app-brainstorming',
  imports: [
    CommonModule,
    UnderstandingComponent,
    UserPersonaComponent,
    PersonaDetailsComponent,
    FeatureListComponent,
    SwotAnalysisComponent,
    ProductRoadmapComponent,
    AweSplitScreenComponent,
    LeftPanelComponent,
    RightPanelComponent,
    ChatPanelComponent,
    LeftPanelHeaderComponent,
    NewRightPanelHeaderComponent,
    NavigationConfirmationModalComponent,
    SummaryComponent,
    // DebugStateComponent,
  ],
  templateUrl: './brainstorming.component.html',
  styleUrl: './brainstorming.component.scss',
  animations: [
    trigger('roboBallAnimation', [
      state(
        'idle',
        style({
          transform: 'scale(1) rotate(0deg)',
          boxShadow: '0px 2px 8px rgba(0, 0, 0, 0.15)',
        }),
      ),
      state(
        'hover',
        style({
          transform: 'scale(1.1) rotate(5deg)',
          boxShadow: '0px 8px 25px rgba(74, 144, 226, 0.3)',
        }),
      ),
      state(
        'clicked',
        style({
          transform: 'scale(0.95) rotate(-5deg)',
          boxShadow: '0px 4px 15px rgba(74, 144, 226, 0.5)',
        }),
      ),
      transition(
        'idle => hover',
        animate('0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)'),
      ),
      transition(
        'hover => idle',
        animate('0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)'),
      ),
      transition('* => clicked', animate('0.15s ease-in')),
      transition('clicked => *', animate('0.3s ease-out')),
    ]),
    trigger('promptAnimation', [
      state(
        'hidden',
        style({
          opacity: 0,
          transform: 'translateX(-20px) scale(0.9)',
        }),
      ),
      state(
        'visible',
        style({
          opacity: 1,
          transform: 'translateX(0) scale(1)',
        }),
      ),
      transition(
        'hidden => visible',
        animate('0.4s 0.1s cubic-bezier(0.25, 0.46, 0.45, 0.94)'),
      ),
      transition('visible => hidden', animate('0.2s ease-in')),
    ]),
  ],
})
export class BrainstormingComponent implements OnInit, OnDestroy {
  roboBallIcon: string = ' icons/robo_ball.svg';
  chevronLeftIcon: string = 'assets/icons/awe_chevron_left.svg';
  chevronRightIcon: string = 'assets/icons/awe_chevron_right.svg';
  // State observables
  currentStep$: Observable<StepperStep | null>;
  currentStepIndex$: Observable<number>;
  canGoNext$: Observable<boolean>;
  canGoPrevious$: Observable<boolean>;
  isLoading$: Observable<boolean>;
  loadingMessage$: Observable<string | null>;
  hasErrors$: Observable<boolean>;
  showSplitScreen$: Observable<boolean>; 
  public projectDetails$: Observable<ProjectDetailsState>;

  // Navigation confirmation modal
  showNavigationModal$: Observable<boolean>;

  // Local state
  currentStep: StepperStep | null = null;
  currentStepIndex: number = 0;

  // Summary display state
  showSummary: boolean = false;

  // Finish button loading state
  isFinishLoading: boolean = false;

  // Header navigation loading state
  isHeaderLoading: boolean = false;
  headerLoadingMessage: string = '';

  // Loading states are now handled by the stepper component

  // State for left panel toggle - using AVA play library directly
  leftPanelWidth: number = 25; // Default width for left panel
  isDarkMode = false;

  // Track if any edit modal is open across all components
  isAnyEditModalOpen = false;

  // Callbacks removed - using AVA play library directly

  // Animation states
  roboBallState = 'idle';
  promptState = 'visible';

  // Persona details state
  showPersonaDetails = false;
  selectedPersonaId: string | null = null;

  // Error states (loading now handled by stepper)
  stepError: string | null = null;

  // Theme observable
  currentTheme$: Observable<'light' | 'dark'>;

  private subscriptions: Subscription[] = [];

  constructor(
    public stepperService: StepperService, // Made public for template access
    private router: Router,
    private pipelineService: ProductPipelineService,
    private appStateService: AppStateService,
    @Inject(NavigationGuardService) private navigationGuardService: NavigationGuardService,
    public panelToggleService: PanelToggleService, // Made public for template access
    private cdr: ChangeDetectorRef,
    private themeService: ThemeServiceService,
    private summaryService: SummaryService,
    
  ) {
    // Initialize state observables
    this.currentStep$ = this.stepperService.currentStep$;
    this.currentStepIndex$ = this.stepperService.currentStepIndex$;
    this.canGoNext$ = this.appStateService.navigationState$.pipe(
      map(state => state.canGoNext)
    );
    this.canGoPrevious$ = this.appStateService.navigationState$.pipe(
      map(state => state.canGoPrevious)
    );
    this.isLoading$ = this.appStateService.selectIsLoading();
    this.currentTheme$ = this.themeService.themeObservable;
    this.loadingMessage$ = this.appStateService.loadingState$.pipe(
      map(state => state.loadingMessage)
    );
    this.hasErrors$ = this.appStateService.selectHasErrors();
    this.showSplitScreen$ = this.appStateService.uiState$.pipe(
      map(state => state.showSplitScreen)
    );
  this.projectDetails$ = this.appStateService.ProjectDetailsState$;

    // Navigation confirmation modal
    this.showNavigationModal$ = this.navigationGuardService.showConfirmationModal$;
  }

  ngOnInit(): void {
    console.log('🔄 BrainstormingComponent: ngOnInit started');

    // Check current app state when component initializes
    const currentPipelineState = this.appStateService.pipelineState;
    console.log('📊 BrainstormingComponent: Current pipeline state on init:', currentPipelineState);
    console.log('📋 BrainstormingComponent: Project name on init:', currentPipelineState.project_name);
    console.log('📋 BrainstormingComponent: Features data on init:', currentPipelineState.data.features);
    console.log('📋 BrainstormingComponent: Run ID on init:', currentPipelineState.run_id);

    const currentProjectDetails = this.appStateService.projectDetailsState;
    console.log('####Initial project details snapshot:', currentProjectDetails.project_name, currentProjectDetails.industry, currentProjectDetails.user_groups);

    // Initialize from stored data if available
    this.initializeFromStoredData();

    // Subscribe to current step changes
    this.subscriptions.push(
      this.stepperService.currentStep$.subscribe((step) => {
        console.log('🔄 BrainstormingComponent: Current step changed to:', step);
        this.currentStep = step;
      }),
    );

    // Subscribe to current step index changes
    this.subscriptions.push(
      this.stepperService.currentStepIndex$.subscribe((index) => {
        console.log('🔄 BrainstormingComponent: Current step index changed to:', index);
        this.currentStepIndex = index;
      }),
    );

    // Subscribe to pipeline state changes to handle API responses
    this.subscriptions.push(
      this.appStateService.pipelineState$.subscribe((pipelineState) => {
        console.log('📊 BrainstormingComponent: Pipeline state updated:', pipelineState);
        console.log('📋 BrainstormingComponent: Project name in state:', pipelineState.project_name);
        console.log('📋 BrainstormingComponent: Features in state:', pipelineState.data.features);

        // When market research API completes, update stepper to show Understanding step
        if (pipelineState.current_step === 'market_research' &&
          pipelineState.data &&
          Object.keys(pipelineState.data).length > 0) {
          console.log('🎯 Market research completed, updating stepper to Understanding step');
          this.stepperService.updateStepFromApiResponse('market_research');
        }
      })
    );

    // Loading state blocking removed to allow continuous step progression
    console.log('✅ BrainstormingComponent: ngOnInit completed');
  }



  finish(): void {
    console.log('🏁 [BRAINSTORMING] ####Finish button clicked - starting loading process');

    // Prevent multiple clicks during loading
    if (this.isFinishLoading) {
      console.log('⚠️ [BRAINSTORMING] ###Finish already in progress, ignoring click');
      return;
    }

    // Start loading state
    this.isFinishLoading = true;
    console.log('⏳ [BRAINSTORMING] Finish button loading started');

    // Get current pipeline state BEFORE any operations
    const currentPipelineState = this.appStateService.pipelineState;
    console.log('📊 [BRAINSTORMING] Current pipeline state before finish:');
    console.log('   📊 run_id:', currentPipelineState.run_id);
    console.log('   📊 project_name:', currentPipelineState.project_name);
    console.log('   📊 current_step:', currentPipelineState.current_step);
    console.log('   📊 completed_steps:', currentPipelineState.completed_steps);
    console.log('   📊 data keys:', Object.keys(currentPipelineState.data || {}));
    console.log('   📊 data content preview:', JSON.stringify(currentPipelineState.data).substring(0, 200) + '...');

    // Trigger progressive summary update to ensure all completed steps are processed
    console.log('🔄 [BRAINSTORMING] Triggering progressive summary update...');
    this.summaryService.triggerProgressiveUpdate();

    // Force summary service to refresh data BEFORE showing summary
    console.log('🔄 [BRAINSTORMING] Forcing summary data refresh...');
    // this.summaryService.forceDataRefresh();

    // Get summary data immediately after refresh
    const summaryDataAfterRefresh = this.summaryService.getCurrentSummaryData();
    console.log("📋 [BRAINSTORMING] ####Summary data after refresh:", summaryDataAfterRefresh);
    console.log("   📋 Summary name:", summaryDataAfterRefresh?.name);
    console.log("   📋 Summary progress:", summaryDataAfterRefresh?.progress + '%');
    console.log("   📋 Summary hasData:", summaryDataAfterRefresh?.hasData);
    console.log("   📋 Summary features count:", summaryDataAfterRefresh?.features?.length || 0);
    console.log("   📋 Summary personas count:", summaryDataAfterRefresh?.personas?.length || 0);

    // Simulate loading time (2 seconds) before showing summary
    setTimeout(() => {
      // Show summary view
      this.showSummary = true;
      this.isFinishLoading = false;

      // Get final summary data to verify it's still available
      const finalSummaryData = this.summaryService.getCurrentSummaryData();
      console.log('🎯 [BRAINSTORMING] ###Summary view displayed, loading complete');
      console.log('   📋 Final summary data name:', finalSummaryData?.name);
      console.log('   📋 Final summary data hasData:', finalSummaryData?.hasData);
    }, 2000);

    // Additional state update to ensure data persistence
    console.log('🔄 [BRAINSTORMING] Performing additional state update for data persistence...');
    const currentState = this.appStateService.pipelineState;

    console.log("📊 [BRAINSTORMING] Current state verification:", {
      name: currentState.project_name,
      steps: Object.keys(currentState.data || {}).length,
      hasFeatures: !!currentState.data?.features,
      hasPersona: !!currentState.data?.persona,
      hasSwot: !!currentState.data?.swot,
      hasRoadmap: !!currentState.data?.roadmap
    });

    // Force state update with latest data to trigger all subscribers
    this.appStateService.updatePipelineState({
      ...currentState,
      lastUpdated: new Date().toISOString()
    });

    console.log('✅ [BRAINSTORMING] Finish method completed - summary should render with data');
  }





  /**
   * Initialize stepper state from current BehaviorSubject state
   */
  private initializeFromStoredData(): void {
      if(this.appStateService.hasStoredData()) {
      const currentState = this.appStateService.pipelineState;
      console.log('🔄 Initializing from current BehaviorSubject state:', currentState);

      // Restore stepper state from current data
      this.stepperService.restoreFromStoredData({
        ...currentState.data,
        current_step: currentState.current_step
      });

      console.log('✅ Successfully initialized from BehaviorSubject state');
    }
  }

  /**
   * Clear stored data and reset stepper (useful for starting new project)
   */
  clearStoredDataAndReset(): void {
    this.appStateService.resetState();
    console.log('🗑️ Cleared stored data and reset centralized state');
  }

  /**
   * Handle navigation confirmation modal cancel
   */
  onNavigationCancel(): void {
    this.navigationGuardService.cancelNavigation();
  }

  /**
   * Handle navigation confirmation modal confirm
   */
  onNavigationConfirm(): void {
    this.navigationGuardService.confirmNavigation();
  }
  ngOnDestroy(): void {
    // Clean up subscriptions
    this.subscriptions.forEach((sub) => sub.unsubscribe());

    console.log('🧹 BrainstormingComponent destroyed, subscriptions cleaned up');
  }

  onStepChanged(event: { step: StepperStep; index: number }): void {
    // Handle step change if needed
    console.log('Step changed:', event);
  }

  // Loading messages are now handled by the stepper component

  nextStep(): void {
    // Allow continuous step progression - blocking removed
    if (this.stepperService.canGoNext()) {
      const nextStepIndex = this.currentStepIndex + 1;
      const nextStep = this.stepperService.getStepByIndex(nextStepIndex);

      if (nextStep) {
        // Clear previous errors
        this.stepError = null;

        // Check if the next step already has data (smart navigation)
        const shouldCallApi = this.stepperService.shouldCallApiForStep(nextStep.id);

        if (shouldCallApi) {
          // Get the API step that should be called for the current stepper step
          const pipelineCurrentStep = this.stepperService.getNextApiStepForCurrentStep();

          if (pipelineCurrentStep) {
            // Start header loading state
            this.isHeaderLoading = true;
            this.headerLoadingMessage = `Loading ${nextStep.label}...`;
            console.log(`🔄 Calling API for step: ${pipelineCurrentStep}`);

            // Call pipeline API for the next step
            this.pipelineService.progressToStep(pipelineCurrentStep).subscribe({
              next: (response) => {
                console.log(`✅ Step ${pipelineCurrentStep} completed:`, response);

                // Update stepper based on API response
                this.stepperService.updateStepFromApiResponse(response.step as any);

                // End header loading state
                this.isHeaderLoading = false;
                this.headerLoadingMessage = '';
              },
              error: (error) => {
                console.error(
                  `❌ Error progressing to step ${pipelineCurrentStep}:`,
                  error,
                );
                this.stepError = `Failed to progress to ${nextStep.label}. Please try again.`;

                // End header loading state on error
                this.isHeaderLoading = false;
                this.headerLoadingMessage = '';
              },
            });
          } else {
            // For steps that don't require API calls, just move forward
            this.stepperService.nextStep();
            this.stepperService.markStepAsCompleted(this.currentStepIndex - 1);
          }
        } else {
          // Step already has data, just navigate without API call
          console.log(`🚀 Step ${nextStep.id} already has data, navigating without API call`);
          this.stepperService.nextStep();
        }
      }
    }
  }

  previousStep(): void {
    this.stepperService.previousStep();
  }

  // Navigation state now handled directly by stepper service in template

  toggleLeftPanel(): void {
    // Implement left panel toggle logic
  }

  // Helper method to get current component name for rendering
  getCurrentComponent(): string {
    return this.currentStep?.component || 'understanding';
  }

  // Helper method to check if a specific component should be shown
  shouldShowComponent(componentName: string): boolean {
    return this.getCurrentComponent() === componentName;
  }

  // Split screen functionality is now handled by AVA play library directly
  // No need for service methods as the component is always visible

  /**
   * Clear step error message
   */
  clearStepError(): void {
    this.stepError = null;
  }

  /**
   * Handle split screen close event
   */
  // onSplitScreenClose(): void {
  //   console.log('🔒 Split screen closed event');
  //   this.roboBallState = 'idle';
  // }

  /**
   * Handle split screen open event
   */
  // onSplitScreenOpen(): void {
  //   console.log('🔓 Split screen opened event');
  //   this.roboBallState = 'idle';
  // }

  onRoboBallHover() {
    if (this.roboBallState === 'idle') {
      this.roboBallState = 'hover';
    }
  }

  onRoboBallLeave() {
    if (this.roboBallState === 'hover') {
      this.roboBallState = 'idle';
    }
  }

  toggleTheme() {
    this.isDarkMode = !this.isDarkMode;
  }

  // --- Persona Details Methods ---
  showPersonaDetailsView(personaId: string): void {
    this.selectedPersonaId = personaId;
    this.showPersonaDetails = true;
  }

  hidePersonaDetailsView(): void {
    this.showPersonaDetails = false;
    this.selectedPersonaId = null;
  }

  // Helper method to check if persona details should be shown
  shouldShowPersonaDetails(): boolean {
    return this.shouldShowComponent('persona') && this.showPersonaDetails;
  }

  // Helper method to check if persona list should be shown
  shouldShowPersonaList(): boolean {
    return this.shouldShowComponent('persona') && !this.showPersonaDetails;
  }
}

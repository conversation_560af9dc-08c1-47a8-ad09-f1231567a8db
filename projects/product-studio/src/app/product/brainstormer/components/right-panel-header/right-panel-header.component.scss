.right-panel-header {
  display: flex;
  min-height: 54px;
  padding: 9px 26px;
  justify-content: space-between;
  align-items: center;
  align-self: stretch;
  border-radius: 12px;
  background: var(--page-header-bg);
  // margin-bottom: 1rem;

  .header-title-container {
    display: flex;
    align-items: center;
    min-height: 40px; // Prevent layout shift during loading
  }

  .header-title {
    font-size: 20px;
    font-weight: bold;
    color: #fff;
    margin: 0;
  }

 

  .header-actions {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1rem;

    .nav-button {
      height: 40px;
      width: 40px;
      padding: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 1px solid #e5e7eb;
      background-color: #fff;
      transition: all 0.2s ease;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      border-radius: 50px;

      &:hover:not(:disabled) {
        background-color: #f8fafc;
        border-color: #d1d5db;
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
      }

      &:active:not(:disabled) {
        transform: translateY(0);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      &:focus {
        box-shadow: 0 0 0 0.2rem rgba(124, 58, 237, 0.25);
        border-color: #7c3aed;
      }

      &:disabled {
        opacity: 0.4;
        cursor: not-allowed;
        background-color: #f9fafb;
        border-color: #e5e7eb;
        transform: none;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      .nav-icon {
        width: 18px;
        height: 18px;
        color: #6b7280;
        transition: color 0.2s ease;
      }

      &:hover:not(:disabled) .nav-icon {
        color: #7c3aed;
      }

      &:disabled .nav-icon {
        color: #9ca3af;
      }
    }
  }
}

// Traffic Loader Animation
@keyframes s8 {
  100% {
    transform: rotate(1turn);
  }
}

.left-panel-header {
  width: 100%;
  height: 54px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(90deg, #8b5cf6 0%, #3c82f6 100%);
  border-radius: 12px;
  padding: 0 16px;
  position: relative;

  // Theme support
  &[data-theme="dark"] {
    background: linear-gradient(90deg, #7c3aed 0%, #2563eb 100%);
  }

  .header-content {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
  }

  .header-left {
    flex: 0 0 auto;
    display: flex;
    align-items: center;
    // Reserved for future logo/branding
  }

  .header-center {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    align-items: center;
    justify-content: center;

    .header-title {
      color: #ffffff;
      font-size: 16px;
      font-weight: 600;
      margin: 0;
      text-align: center;
      letter-spacing: 0.5px;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }
  }

  .header-right {
    flex: 0 0 auto;
    display: flex;
    align-items: center;
    justify-content: flex-end;

    .toggle-button {
      width: 32px;
      height: 32px;
      border: none;
      background: rgba(255, 255, 255, 0.2);
      border-radius: 6px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.2s ease;
      backdrop-filter: blur(10px);

      &:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: translateY(-1px);
      }

      &:active {
        transform: translateY(0);
        background: rgba(255, 255, 255, 0.25);
      }

      &:focus {
        outline: none;
        box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.4);
      }

      svg {
        color: #ffffff;
        transition: transform 0.2s ease;
      }

      &.collapsed svg {
        transform: rotate(180deg);
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .left-panel-header {
    height: 48px;
    padding: 0 12px;

    .header-center .header-title {
      font-size: 14px;
    }

    .header-right .toggle-button {
      width: 28px;
      height: 28px;

      svg {
        width: 14px;
        height: 14px;
      }
    }
  }
}

@media (max-width: 480px) {
  .left-panel-header {
    height: 44px;
    padding: 0 8px;

    .header-center .header-title {
      font-size: 13px;
    }

    .header-right .toggle-button {
      width: 26px;
      height: 26px;

      svg {
        width: 12px;
        height: 12px;
      }
    }
  }
}

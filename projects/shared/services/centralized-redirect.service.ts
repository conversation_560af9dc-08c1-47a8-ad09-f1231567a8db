import { Injectable } from '@angular/core';
import { Router } from '@angular/router';

export interface AppRedirectConfig {
  appName: string;
  baseUrl: string;
  defaultRoute: string;
  localPort?: number; // Port for local development
}

@Injectable({
  providedIn: 'root'
})
export class CentralizedRedirectService {
  
  // Configuration for all apps
  private readonly appConfigs: AppRedirectConfig[] = [
    {
      appName: 'elder-wand',
      baseUrl: '/launchpad',
      defaultRoute: '/launchpad/',
      localPort: 4200
    },
    {
      appName: 'console',
      baseUrl: '/console',
      defaultRoute: '/console/',
      localPort: 4203
    },
    {
      appName: 'experience-studio',
      baseUrl: '/experience',
      defaultRoute: '/experience/',
      localPort: 4201
    },
    {
      appName: 'product-studio',
      baseUrl: '/product',
      defaultRoute: '/product/',
      localPort: 4202
    }
  ];

  constructor(private router: Router) {}

  /**
   * Check if we're running in local development mode
   */
  private isLocalDevelopment(): boolean {
    const port = window.location.port;
    
    // For Docker setup: all apps run on localhost without ports
    if (window.location.hostname === 'localhost' && (port === '' || port === '80' || port === '443')) {
      return false; // This is Docker setup, not traditional local dev
    }
    
    // For traditional local development with ports
    return port !== '' && ['4200', '4201', '4202', '4203', '4204'].includes(port);
  }

  /**
   * Get the marketing app URL (local or production)
   */
  private getMarketingLoginUrl(): string {
    if (this.isLocalDevelopment()) {
      // In traditional local dev, marketing runs on port 4204
      return `http://localhost:4204/login`;
    } else {
      // In production or Docker setup, use same origin
      return `${window.location.origin}/login`;
    }
  }

  /**
   * Set a cookie with cross-app compatibility
   */
  private setCookie(name: string, value: string, days: number = 1): void {
    const expires = new Date();
    expires.setTime(expires.getTime() + (days * 24 * 60 * 60 * 1000));
    
    // Build cookie string with appropriate settings for different environments
    let cookieString = `${name}=${encodeURIComponent(value)};expires=${expires.toUTCString()};path=/`;
    
    // Add domain and security settings based on environment
    if (this.isLocalDevelopment()) {
      // For localhost development
      cookieString += ';SameSite=Lax';
    } else {
      // For production - add domain and security
      const domain = this.getCookieDomain();
      if (domain) {
        cookieString += `;domain=${domain}`;
      }
      cookieString += ';SameSite=Lax;Secure';
    }
    
    document.cookie = cookieString;
    console.log(`Cookie set: ${name}=${value} (${this.isLocalDevelopment() ? 'local' : 'production'})`);
  }

  /**
   * Get a cookie value
   */
  private getCookie(name: string): string | null {
    const nameEQ = name + "=";
    const ca = document.cookie.split(';');
    
    for (let i = 0; i < ca.length; i++) {
      let c = ca[i];
      while (c.charAt(0) === ' ') c = c.substring(1, c.length);
      if (c.indexOf(nameEQ) === 0) {
        const value = decodeURIComponent(c.substring(nameEQ.length, c.length));
        console.log(`Cookie found: ${name}=${value}`);
        return value;
      }
    }
    return null;
  }

  /**
   * Delete a cookie
   */
  private deleteCookie(name: string): void {
    document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 UTC;path=/;`;
    console.log(`Cookie deleted: ${name}`);
  }

  /**
   * Get appropriate cookie domain for current environment
   */
  private getCookieDomain(): string | null {
    const hostname = window.location.hostname;
    
    // For localhost, don't set domain
    if (hostname === '' || hostname === '127.0.0.1') {
      return null;
    }
    
    // For production, use the domain
    // If it's a subdomain like 'app.example.com', we might want '.example.com'
    // For now, use the current hostname
    return hostname;
  }

  /**
   * Store the intended destination before redirecting to login
   * @param currentUrl The current URL user was trying to access
   */
  storeIntendedDestination(currentUrl: string): void {
    // IMPORTANT: Don't store URLs that contain authorization codes
    if (currentUrl.includes('code=') || currentUrl.includes('token=')) {
      console.warn('URL contains authorization code or token, not storing as intended destination:', currentUrl);
      return;
    }

    // Check for URL-encoded authorization codes
    if (currentUrl.includes('%2F%3Fcode%3D') || currentUrl.includes('%2F%3Ftoken%3D')) {
      console.warn('URL contains URL-encoded authorization code or token, not storing as intended destination:', currentUrl);
      return;
    }

    // Extract the path from the full URL
    const url = new URL(currentUrl);
    const path = url.pathname + url.search + url.hash;
    
    // Additional check: don't store if path contains code
    if (path.includes('code=') || path.includes('token=')) {
      console.warn('Path contains authorization code or token, not storing as intended destination:', path);
      return;
    }
    
    // Additional check: don't store if path contains URL-encoded code
    if (path.includes('%2F%3Fcode%3D') || path.includes('%2F%3Ftoken%3D')) {
      console.warn('Path contains URL-encoded authorization code or token, not storing as intended destination:', path);
      return;
    }
    
    // Store in sessionStorage for immediate access
    sessionStorage.setItem('intendedDestination', path);
    console.log('Stored intended destination in sessionStorage:', path);
    
    // Store in cookie for cross-app access (expires in 1 hour)
    this.setCookie('intendedDestination', path, 1/24); // 1 hour
    console.log('Stored intended destination in cookie:', path);
  }

  /**
   * Get the stored intended destination
   * @returns The stored URL or null if none exists
   */
  getIntendedDestination(): string | null {
    console.log('Getting intended destination...');
    
    // Try sessionStorage first (for same-app access)
    let destination = sessionStorage.getItem('intendedDestination');
    console.log('From sessionStorage:', destination);
    
    // If not found in sessionStorage, try cookie (for cross-app scenarios)
    if (!destination) {
      destination = this.getCookie('intendedDestination');
      console.log('From cookie:', destination);
      if (destination) {
        console.log('Found intended destination in cookie:', destination);
        // Move it to sessionStorage for consistency
        sessionStorage.setItem('intendedDestination', destination);
      }
    }
    
    // Also check URL parameters for returnUrl (fallback)
    if (!destination) {
      const urlParams = new URLSearchParams(window.location.search);
      const returnUrl = urlParams.get('returnUrl');
      console.log('From URL params (returnUrl):', returnUrl);
      if (returnUrl) {
        console.log('Found returnUrl in URL parameters:', returnUrl);
        destination = returnUrl;
        // Store it for future use
        sessionStorage.setItem('intendedDestination', destination);
        this.setCookie('intendedDestination', destination, 1/24);
      }
    }
    
    // IMPORTANT: Check if destination looks like an authorization code and ignore it
    if (destination && destination.includes('code=')) {
      console.warn('Destination looks like an authorization code, ignoring:', destination);
      destination = null;
    }
    
    // IMPORTANT: Check if destination is just a code parameter
    if (destination && destination.startsWith('/?code=')) {
      console.warn('Destination is just a code parameter, ignoring:', destination);
      destination = null;
    }
    
    // IMPORTANT: Check if destination contains URL-encoded authorization code
    if (destination && (destination.includes('%2F%3Fcode%3D') || destination.includes('%2F%3Ftoken%3D'))) {
      console.warn('Destination contains URL-encoded authorization code, ignoring:', destination);
      destination = null;
    }
    
    // IMPORTANT: Check if destination is a long encoded string (likely an auth code)
    if (destination && destination.length > 200 && destination.includes('%')) {
      console.warn('Destination is a long encoded string (likely auth code), ignoring:', destination);
      destination = null;
    }
    
    console.log('Final intended destination:', destination);
    return destination;
  }

  /**
   * Clear the stored intended destination
   */
  clearIntendedDestination(): void {
    sessionStorage.removeItem('intendedDestination');
    this.deleteCookie('intendedDestination');
  }

  /**
   * Determine the appropriate redirect URL after successful login
   * @returns The URL to redirect to after login
   */
  getPostLoginRedirectUrl(): string {
    const intendedDestination = this.getIntendedDestination();
    
    if (intendedDestination) {
      console.log('Found intended destination:', intendedDestination);
      
      // If intended destination is just root (/), redirect to launchpad as default
      if (intendedDestination === '/' || intendedDestination === '') {
        console.log('Intended destination is root, redirecting to launchpad as default');
        this.clearIntendedDestination();
        return '/launchpad/';
      }
      
      // Check if the intended destination is from one of our apps
      const appConfig = this.findAppConfigByUrl(intendedDestination);
      
      if (appConfig) {
        console.log('Redirecting to intended destination:', intendedDestination);
        console.log('App config found:', appConfig);
        // User was trying to access a specific app, redirect them back
        this.clearIntendedDestination();
        return intendedDestination;
      } else {
        console.log('No app config found for intended destination:', intendedDestination);
      }
    }

    // Default redirect to Elder Wand
    console.log('No intended destination found, redirecting to Elder Wand');
    return '/launchpad/';
  }

  /**
   * Find app configuration based on URL
   * @param url The URL to check
   * @returns App config if found, null otherwise
   */
  private findAppConfigByUrl(url: string): AppRedirectConfig | null {
    // Remove leading slash for comparison
    const cleanUrl = url.startsWith('/') ? url : `/${url}`;
    
    console.log('Finding app config for URL:', cleanUrl);
    console.log('Available app configs:', this.appConfigs.map(c => ({ name: c.appName, baseUrl: c.baseUrl })));
    
    const foundConfig = this.appConfigs.find(config => {
      // Check if URL starts with the app's base URL
      const matches = cleanUrl.startsWith(config.baseUrl);
      console.log(`Checking ${config.appName} (${config.baseUrl}): ${matches}`);
      return matches;
    });
    
    if (foundConfig) {
      console.log('Found matching app config:', foundConfig);
    } else {
      console.log('No matching app config found');
    }
    
    return foundConfig || null;
  }

  /**
   * Check if current URL is from a specific app
   * @param url The URL to check
   * @param appName The app name to check for
   * @returns True if URL belongs to the specified app
   */
  isUrlFromApp(url: string, appName: string): boolean {
    const appConfig = this.appConfigs.find(config => config.appName === appName);
    return appConfig ? url.startsWith(appConfig.baseUrl) : false;
  }

  /**
   * Get the default route for a specific app
   * @param appName The app name
   * @returns The default route for the app
   */
  getDefaultRouteForApp(appName: string): string {
    const appConfig = this.appConfigs.find(config => config.appName === appName);
    return appConfig ? appConfig.defaultRoute : '/launchpad/';
  }

  /**
   * Redirect to marketing login with return URL parameter
   * @param returnUrl The URL to return to after login
   */
  redirectToMarketingLogin(returnUrl?: string): void {
    if (returnUrl) {
      this.storeIntendedDestination(returnUrl);
    }
    
    console.log('Redirecting to marketing login:', this.getMarketingLoginUrl());
    // Redirect to marketing login (handles local vs production)
    window.location.href = this.getMarketingLoginUrl();
  }

  /**
   * Handle post-login redirect
   */
  handlePostLoginRedirect(): void {
    const redirectUrl = this.getPostLoginRedirectUrl();
    
    console.log('Handling post-login redirect to:', redirectUrl);
    console.log('Current location:', window.location.href);
    console.log('Is local development:', this.isLocalDevelopment());
    
    if (this.isLocalDevelopment()) {
      // In local dev, determine which app to redirect to based on the URL
      const appConfig = this.findAppConfigByUrl(redirectUrl);
      if (appConfig && appConfig.localPort) {
        // Redirect to the specific local port
        const localUrl = `http://localhost:${appConfig.localPort}${redirectUrl}`;
        console.log('Redirecting to local app:', localUrl);
        window.location.href = localUrl;
        return;
      }
      // Default to Elder Wand (port 4200)
      const defaultLocalUrl = `http://localhost:4200${redirectUrl}`;
      console.log('Redirecting to default local app:', defaultLocalUrl);
      window.location.href = defaultLocalUrl;
    } else {
      // In production, use same origin
      const productionUrl = `${window.location.origin}${redirectUrl}`;
      console.log('Redirecting to production app:', productionUrl);
      window.location.href = productionUrl;
    }
  }
} 
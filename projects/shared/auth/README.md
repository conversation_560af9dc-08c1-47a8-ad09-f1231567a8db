# Authentication System

This authentication system ensures consistent login behavior across all AAVA applications (<PERSON><PERSON><PERSON>, <PERSON> Wand, Experience Studio, and Product Studio).

## Overview

The authentication system is centralized in the `shared/auth` folder and provides:

1. **Consistent Token Management**: All apps use the same token storage service that stores tokens in cookies
2. **Automatic Authentication Checking**: Apps automatically check for valid tokens on initialization
3. **Unified Login Flow**: All apps redirect to login when no valid tokens are found
4. **Shared Components**: Login and callback components are shared across all apps

## Key Components

### AuthStateService
- **Purpose**: Central service for managing authentication state across all apps
- **Key Methods**:
  - `isAuthenticated()`: Checks if user has valid tokens
  - `checkAuthenticationAndRedirect()`: Checks auth status and redirects to login if needed
  - `onAuthenticationSuccess()`: Called when login is successful
  - `onLogout()`: Called when user logs out

### AuthGuard
- **Purpose**: Route guard that protects routes requiring authentication
- **Behavior**: 
  - Checks for access token and refresh token
  - Attempts token refresh if only refresh token exists
  - Redirects to login if no valid tokens found

### TokenStorageService
- **Purpose**: Manages token storage in cookies
- **Features**:
  - Stores tokens with proper domain settings
  - Handles both access and refresh tokens
  - Manages user information and login type

## How It Works

### App Initialization
1. Each app calls `authStateService.checkAuthenticationAndRedirect()` in `ngOnInit()`
2. If no valid tokens exist, user is automatically redirected to login
3. If tokens exist, app continues normal initialization

### Login Flow
1. User navigates to `/login` route
2. Login component checks if user is already authenticated
3. If authenticated, redirects to post-login URL
4. If not authenticated, shows login form

### Authentication Success
1. After successful login, `authStateService.onAuthenticationSuccess()` is called
2. User is redirected to the appropriate post-login URL for that app
3. Auth state is updated across the application

### Logout Flow
1. User clicks logout in header
2. `authStateService.onLogout()` is called
3. Tokens are cleared from storage
4. User is redirected to login page

## App-Specific Configuration

Each app has its own authentication configuration:

### Console App
- **Post-login URL**: `/build/agents`
- **Auth Config**: `environment.consoleApiAuthUrl`

### Elder Wand App
- **Post-login URL**: `/dashboard`
- **Auth Config**: `environment.elderWandApiAuthUrl`

### Experience Studio App
- **Post-login URL**: `/dashboard`
- **Auth Config**: `environment.experianceApiAuthUrl`

### Product Studio App
- **Post-login URL**: `/product`
- **Auth Config**: `environment.productStudioApiAuthUrl`

## Token Management

### Cookie Storage
- Tokens are stored in cookies with proper domain settings
- Access tokens have expiration times
- Refresh tokens are stored for longer periods
- All tokens are cleared on logout

### Token Refresh
- Automatic token refresh when access token expires
- Uses refresh token to get new access token
- Seamless user experience during token refresh

## Security Features

1. **Domain-specific cookies**: Tokens are stored with proper domain restrictions
2. **Secure flags**: Cookies use secure flags when on HTTPS
3. **SameSite policy**: Cookies use strict SameSite policy
4. **Automatic cleanup**: Tokens are automatically cleared on logout

## Usage in Apps

### Adding to New App
1. Import `AuthModule` in your app module
2. Inject `AuthStateService` in your main component
3. Call `checkAuthenticationAndRedirect()` in `ngOnInit()`
4. Add `AuthGuard` to protected routes

### Example Implementation
```typescript
import { AuthStateService } from '@shared/auth/services/auth-state.service';

export class AppComponent implements OnInit {
  constructor(private authStateService: AuthStateService) {}

  ngOnInit() {
    if (!this.authStateService.checkAuthenticationAndRedirect()) {
      return; // Don't continue if not authenticated
    }
    // Continue with app initialization
  }
}
```

## Troubleshooting

### Common Issues
1. **Infinite redirect loops**: Check that login route is not protected by AuthGuard
2. **Token not found**: Verify cookie domain settings match your app domain
3. **Login not working**: Check that auth configuration is properly set

### Debugging
- Check browser cookies for token presence
- Verify auth configuration in environment files
- Check console for authentication-related errors 
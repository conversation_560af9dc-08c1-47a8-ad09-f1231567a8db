import { TestBed } from '@angular/core/testing';
import { Router } from '@angular/router';
import { of, throwError } from 'rxjs';
import { AuthTokenService } from './auth-token.service';
import { AuthService } from './auth.service';
import { TokenStorageService } from './token-storage.service';
import { CentralizedRedirectService } from '@shared/services/centralized-redirect.service';

describe('AuthTokenService', () => {
  let service: AuthTokenService;
  let mockRouter: jasmine.SpyObj<Router>;
  let mockAuthService: jasmine.SpyObj<AuthService>;
  let mockTokenStorageService: jasmine.SpyObj<TokenStorageService>;
  let mockCentralizedRedirectService: jasmine.SpyObj<CentralizedRedirectService>;

  beforeEach(() => {
    const routerSpy = jasmine.createSpyObj('Router', ['navigate', 'navigateByUrl']);
    const authServiceSpy = jasmine.createSpyObj('AuthService', ['basicRefreshToken', 'refreshToken']);
    const tokenStorageServiceSpy = jasmine.createSpyObj('TokenStorageService', [
      'getAccessToken', 'getRefreshToken', 'getLoginType', 'deleteCookie', 'clearTokens'
    ]);
    const centralizedRedirectServiceSpy = jasmine.createSpyObj('CentralizedRedirectService', [
      'storeIntendedDestination', 'handlePostLoginRedirect'
    ]);

    TestBed.configureTestingModule({
      providers: [
        AuthTokenService,
        { provide: Router, useValue: routerSpy },
        { provide: AuthService, useValue: authServiceSpy },
        { provide: TokenStorageService, useValue: tokenStorageServiceSpy },
        { provide: CentralizedRedirectService, useValue: centralizedRedirectServiceSpy }
      ]
    });

    service = TestBed.inject(AuthTokenService);
    mockRouter = TestBed.inject(Router) as jasmine.SpyObj<Router>;
    mockAuthService = TestBed.inject(AuthService) as jasmine.SpyObj<AuthService>;
    mockTokenStorageService = TestBed.inject(TokenStorageService) as jasmine.SpyObj<TokenStorageService>;
    mockCentralizedRedirectService = TestBed.inject(CentralizedRedirectService) as jasmine.SpyObj<CentralizedRedirectService>;
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('checkTokenExpiry', () => {
    beforeEach(() => {
      // Mock window.location
      Object.defineProperty(window, 'location', {
        value: { origin: 'http://localhost:4200' },
        writable: true
      });
    });

    it('should not refresh token when expiry is more than 5 minutes away', () => {
      // Set expiry time to 10 minutes from now
      const futureTime = new Date();
      futureTime.setMinutes(futureTime.getMinutes() + 10);
      localStorage.setItem('access_token_expires', futureTime.toISOString());

      mockTokenStorageService.getLoginType.and.returnValue('basic');
      mockAuthService.basicRefreshToken.and.returnValue(of({
        token_type: 'Bearer',
        scope: 'openid profile email',
        expires_in: 3600,
        access_token: 'mock-access-token',
        id_token: 'mock-id-token',
        refresh_token: 'mock-refresh-token',
        user_name: 'testuser',
        email: '<EMAIL>'
      }));

      service.checkTokenExpiry();

      expect(mockTokenStorageService.deleteCookie).not.toHaveBeenCalled();
      expect(mockAuthService.basicRefreshToken).not.toHaveBeenCalled();
    });

    it('should refresh basic token when expiry is within 5 minutes', () => {
      // Set expiry time to 3 minutes from now
      const futureTime = new Date();
      futureTime.setMinutes(futureTime.getMinutes() + 3);
      localStorage.setItem('access_token_expires', futureTime.toISOString());

      mockTokenStorageService.getLoginType.and.returnValue('basic');
      mockAuthService.basicRefreshToken.and.returnValue(of({
        token_type: 'Bearer',
        scope: 'openid profile email',
        expires_in: 3600,
        access_token: 'mock-access-token',
        id_token: 'mock-id-token',
        refresh_token: 'mock-refresh-token',
        user_name: 'testuser',
        email: '<EMAIL>'
      }));

      service.checkTokenExpiry();

      expect(mockTokenStorageService.deleteCookie).toHaveBeenCalledWith('access_token');
      expect(mockAuthService.basicRefreshToken).toHaveBeenCalled();
    });

    it('should refresh SSO token when expiry is within 5 minutes', () => {
      // Set expiry time to 2 minutes from now
      const futureTime = new Date();
      futureTime.setMinutes(futureTime.getMinutes() + 2);
      localStorage.setItem('access_token_expires', futureTime.toISOString());

      mockTokenStorageService.getLoginType.and.returnValue('sso');
      mockTokenStorageService.getRefreshToken.and.returnValue('refresh-token-123');
      mockAuthService.refreshToken.and.returnValue(of({
        accessToken: 'new-access-token',
        refreshToken: 'new-refresh-token'
      }));

      service.checkTokenExpiry();

      expect(mockTokenStorageService.deleteCookie).toHaveBeenCalledWith('access_token');
      expect(mockAuthService.refreshToken).toHaveBeenCalledWith('refresh-token-123');
    });

    it('should redirect to login when no refresh token available', () => {
      // Set expiry time to 1 minute from now
      const futureTime = new Date();
      futureTime.setMinutes(futureTime.getMinutes() + 1);
      localStorage.setItem('access_token_expires', futureTime.toISOString());

      mockTokenStorageService.getLoginType.and.returnValue('sso');
      mockTokenStorageService.getRefreshToken.and.returnValue(null);

      service.checkTokenExpiry();

      expect(mockTokenStorageService.deleteCookie).toHaveBeenCalledWith('access_token');
      expect(mockCentralizedRedirectService.storeIntendedDestination).toHaveBeenCalled();
    });

    it('should not do anything when no expiry time is stored', () => {
      localStorage.removeItem('access_token_expires');

      service.checkTokenExpiry();

      expect(mockTokenStorageService.deleteCookie).not.toHaveBeenCalled();
      expect(mockAuthService.basicRefreshToken).not.toHaveBeenCalled();
      expect(mockAuthService.refreshToken).not.toHaveBeenCalled();
    });

    it('should not refresh when token has already expired', () => {
      // Set expiry time to 1 minute ago
      const pastTime = new Date();
      pastTime.setMinutes(pastTime.getMinutes() - 1);
      localStorage.setItem('access_token_expires', pastTime.toISOString());

      service.checkTokenExpiry();

      expect(mockTokenStorageService.deleteCookie).not.toHaveBeenCalled();
      expect(mockAuthService.basicRefreshToken).not.toHaveBeenCalled();
      expect(mockAuthService.refreshToken).not.toHaveBeenCalled();
    });
  });

  afterEach(() => {
    localStorage.removeItem('access_token_expires');
  });
}); 
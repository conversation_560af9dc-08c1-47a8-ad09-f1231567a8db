/* ====================
   Main Nav Item Styles
==================== */
.nav-item {
  display: flex;
  align-items: center;
  padding: 8px;
  gap: 4px;
  border-radius: 999px;
  cursor: pointer;
  color: #374151;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  white-space: nowrap;
  position: relative;

  &.selected {
    font-weight: 600;

    .dropdown-arrow svg {
      stroke: currentColor;
    }

    .nav-icon {
      filter: none;
    }

    /* --- Project-Specific Selected Colors --- */
    &.console {
      color: #215ad6;

      .item-label {
        color: #215ad6;
      }

      .nav-icon.selected {
        filter: brightness(0) saturate(100%) invert(31%) sepia(95%) saturate(1381%) hue-rotate(205deg) brightness(94%) contrast(104%);
      }
    }

    &.experience {
      color: #7135D8;

      .item-label {
        color: #7135D8;
      }

      .nav-icon.selected {
        filter: brightness(0) saturate(100%) invert(25%) sepia(56%) saturate(1504%) hue-rotate(254deg) brightness(93%) contrast(102%);
      }
    }

    &.elder {
      color:  #E91E63;;

      .item-label {
        color:  #E91E63;;
      }

      .nav-icon.selected {
        filter: brightness(0) saturate(100%) invert(27%) sepia(90%) saturate(1625%) hue-rotate(313deg) brightness(96%) contrast(105%);
      }
    }

    &.product {
      color: #7135D8;

      .item-label {
        color: #7135D8;
      }

      .nav-icon.selected {
        filter: brightness(0) saturate(100%) invert(25%) sepia(56%) saturate(1504%) hue-rotate(254deg) brightness(93%) contrast(102%);
      }
    }
  }

  &.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;

    .item-icon,
    .item-label,
    .dropdown-arrow {
      opacity: 0.6;
    }

    .nav-icon {
      opacity: 0.4;
      filter: grayscale(100%);
    }
  }
}

.item-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  transition: all 0.3s ease;
}

.nav-icon {
  width: 20px;
  height: 20px;
  object-fit: contain;
  transition: all 0.3s ease;

  // base color filter: convert to black/gray
  filter: grayscale(1) brightness(0) invert(0);
}

.nav-icon.selected {
  // blue tint: same as selected text color
  filter: brightness(0) saturate(100%) invert(31%) sepia(95%) saturate(1381%) hue-rotate(205deg) brightness(94%) contrast(104%);
}

.item-label {
  font-family: "Mulish", sans-serif;
  transition: all 0.3s ease;
}

.dropdown-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  transition: transform 0.3s ease;

  &.open {
    transform: rotate(180deg);
  }

  svg {
    color: currentColor;
    transition: color 0.3s ease;
  }
}

// Responsive design
@media (max-width: 768px) {
  .nav-item {
    padding: 8px 12px;
    font-size: 13px;
  }

  .item-icon {
    width: 18px;
    height: 18px;
  }

  .nav-icon {
    width: 18px;
    height: 18px;
  }
}

// Dark theme support
@media (prefers-color-scheme: dark) {
  .nav-item {
    color: #e5e7eb;

    &.selected {
      color: rgb(var(--rgb-brand-primary));
    }
    svg {
      stroke: rgb(var(--rgb-brand-primary));
    }
  }
}

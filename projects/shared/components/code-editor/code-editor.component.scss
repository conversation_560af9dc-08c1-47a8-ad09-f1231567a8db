.code-editor-container {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background: #ffffff;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  position: relative;
}

.editor-header {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 16px 20px;
  background: #ffffff;
}

.title-action-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.editor-title {
  margin: 0;
  font-size: 18px;
  font-weight: 700;
  color: #000000;
}

.editor-actions {
  display: flex;
  gap: 8px;
}

.run-btn-wrapper {
  margin-left: auto;
  display: flex;
  align-items: center;
}

::ng-deep .run-black-btn .btn {
  background: #000 !important;
  color: #fff !important;
  border: none !important;
  border-radius: 6px;
  font-size: 14px;
  height: 32px;
  padding: 6px 12px;

  &:hover:not(:disabled) {
    background: #222 !important;
  }
}

.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
  min-height: 200px;
  background: #ffffff;
}

.loading-spinner {
  width: 24px;
  height: 24px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #0969da;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 12px;
}

.loading-container p {
  margin: 0;
  color: #656d76;
  font-size: 14px;
}

.error-container {
  color: #cf222e;

  .error-icon {
    font-size: 24px;
    margin-bottom: 8px;
  }

  h4 {
    margin: 0 0 8px 0;
    font-size: 16px;
    color: #cf222e;
  }

  p {
    margin: 0 0 16px 0;
    font-size: 14px;
    color: #656d76;
  }
}

.monaco-editor-container {
  flex: 1;
  min-height: 300px;
  position: relative;
  background: rgba(26, 70, 167, 0.08);

  &.hidden {
    display: none;
  }
}

.editor-footer {
  padding: 12px 20px;
  background: #ffffff;

  .footer-note {
    margin: 0;
    font-size: 12px;
    color: #656d76;
    line-height: 1.4;

    strong {
      color: #24292f;
      font-weight: 600;
    }
  }
}

.editor-loader-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.5);
  backdrop-filter: blur(4px);
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;

  // .editor-loader-spinner {
  //   width: 48px;
  //   height: 48px;
  // }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@media (max-width: 768px) {
  .editor-header {
    padding: 12px 16px;
  }

  .title-action-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .monaco-editor-container {
    min-height: 250px;
  }

  .editor-title {
    font-size: 16px;
  }
}
.required-asterisk {
  color: red;
  margin-left: 4px;
  font-size: 0.90rem; 
  font-weight: normal;
}
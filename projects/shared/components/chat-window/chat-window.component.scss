.chat-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  position: relative;
  background-color: #f8f9fa;
  
  &.dark {
    background-color: #1a1a1a;
  }
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
  scroll-behavior: smooth;
}

.user-card, .ai-card {
  margin-bottom: 1rem;
  padding: 0.75rem 1rem;
  border-radius: 10px;
  width: fit-content;
  max-width: 85%;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  word-break: break-word;
  
  .card-content {
    position: relative;
  }
}

.user-card {
  margin-left: auto;
  background-color: #e7f3ff;
  color: #333;
  border-bottom-right-radius: 0;
  
  &.dark {
    background-color: #2d3748;
    color: #f8f9fa;
  }
}

.ai-card {
  margin-right: auto;
  background-color: #f0f0f0;
  color: #333;
  border-bottom-left-radius: 0;
  
  &.dark {
    background-color: #1e293b;
    color: #f8f9fa;
  }
}

.markdown-content {
  ::ng-deep {
    p {
      margin: 0 0 0.5rem 0;
      &:last-child {
        margin-bottom: 0;
      }
    }
    
    pre {
      background-color: rgba(0, 0, 0, 0.05);
      border-radius: 4px;
      padding: 0.5rem;
      overflow-x: auto;
      margin: 0.5rem 0;
      
      code {
        font-family: 'Courier New', Courier, monospace;
        white-space: pre;
      }
    }
    
    code {
      background-color: rgba(0, 0, 0, 0.05);
      padding: 0.1rem 0.3rem;
      border-radius: 3px;
      font-family: 'Courier New', Courier, monospace;
    }
    
    ul, ol {
      margin: 0.5rem 0;
      padding-left: 1.5rem;
    }
    
    a {
      color: #0066cc;
      text-decoration: none;
      
      &:hover {
        text-decoration: underline;
      }
    }
    
    img {
      max-width: 100%;
      border-radius: 4px;
    }
    
    h1, h2, h3, h4, h5, h6 {
      margin: 0.5rem 0;
      font-weight: 600;
    }
  }
}

.prompt-bar {
  display: flex;
  padding: 1rem;
  background-color: #fff;
  border-top: 1px solid #e0e0e0;
  align-items: center;
  
  &.dark {
    background-color: #2a2a2a;
    border-top: 1px solid #3a3a3a;
  }
  
  .prompt-input {
    flex: 1;
    padding: 0.75rem 1rem;
    border: 1px solid #ccc;
    border-radius: 20px;
    outline: none;
    font-size: 14px;
    background-color: #f5f5f5;
    
    &:focus {
      border-color: #6566cd;
      box-shadow: 0 0 0 2px rgba(101, 102, 205, 0.2);
    }
    
    &:disabled {
      background-color: #e9ecef;
      cursor: not-allowed;
    }
  }
  
  &.dark .prompt-input {
    background-color: #333;
    border-color: #444;
    color: #f0f0f0;
    
    &:focus {
      border-color: #6566cd;
    }
    
    &:disabled {
      background-color: #2a2a2a;
    }
  }
}

.prompt-icons {
  display: flex;
  margin-left: 0.5rem;
  
  .prompt-icon {
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    margin-left: 0.5rem;
    cursor: pointer;
    background-color: #f0f0f0;
    
    &:hover {
      background-color: #e0e0e0;
    }
  }
  
  .dark & .prompt-icon {
    background-color: #333;
    
    &:hover {
      background-color: #444;
    }
  }
}

.icon-name {
  font-size: 0;
  // This will be replaced with actual icon in production
  // Using placeholder text for development
  &::before {
    content: "📎";
    font-size: 16px;
  }
}

.loading-dots {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 0.5rem;
  
  .dot {
    width: 8px;
    height: 8px;
    margin: 0 3px;
    border-radius: 50%;
    background-color: #666;
    animation: dot-pulse 1.5s infinite ease-in-out;
    
    &:nth-child(1) {
      animation-delay: 0s;
    }
    
    &:nth-child(2) {
      animation-delay: 0.3s;
    }
    
    &:nth-child(3) {
      animation-delay: 0.6s;
    }
  }
  
  .dark & .dot {
    background-color: #ccc;
  }
}

.selected-image {
  margin-bottom: 0.5rem;
  
  .image-preview {
    cursor: pointer;
    
    .thumbnail-image {
      max-width: 200px;
      max-height: 150px;
      border-radius: 6px;
    }
  }
}

.preview-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.preview-content {
  background-color: white;
  border-radius: 8px;
  max-width: 80%;
  max-height: 80%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid #eee;
  
  .preview-title {
    font-weight: 600;
  }
  
  button {
    background: none;
    border: none;
    cursor: pointer;
    font-size: 1.5rem;
    line-height: 1;
    padding: 0 0.5rem;
    
    &:hover {
      color: #6566cd;
    }
  }
}

.preview-body {
  padding: 1rem;
  overflow: auto;
  
  img {
    max-width: 100%;
    max-height: 70vh;
  }
}

.prompt-spacer {
  height: 70px;
}

@keyframes dot-pulse {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.6;
  }
  40% {
    transform: scale(1.2);
    opacity: 1;
  }
}
.pagination-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 16px 0;
  width: 100%;
}

.page-nav {
  color: var(--pagination-arrow-color);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 0;
  background-color: transparent;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 18px;
  font-weight: 500;

  &:hover:not(.disabled) {
    color: var(--pagination-arrow-hover-color);
    background: transparent;
    border-color: transparent;
  }

  &:focus {
    outline: none;
  }

  &.disabled {
    opacity: var(--pagination-disabled-opacity);
    cursor: not-allowed;
    pointer-events: none;
    color: var(--pagination-disabled-text);
  }

  svg {
    width: 16px;
    height: 16px;
  }
}

.page-numbers {
  display: flex;
  align-items: center;
  gap: 8px;
}

.page-number {
  background-color: var(--pagination-button-bg);
  color: var(--pagination-button-text);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: none;
  cursor: pointer;
  font-size: 16px;
  font-weight: 500;
  transition: all 0.2s ease;

  &:hover:not(.active) {
    color: var(--pagination-button-text-hover);
    background: var(--pagination-button-bg-hover);
  }

  &:focus {
    outline: none;
  }

  &.active {
    color: var(--pagination-active-text);
    background: var(--pagination-active-bg);
    border: none;
  }
}

.ellipsis {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  font-size: 16px;
  font-weight: 500;
  color: var(--pagination-ellipsis);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .pagination-container {
    padding: 12px 0;
  }

  .page-nav,
  .page-number {
    width: 36px;
    height: 36px;
  }
}

@media (max-width: 480px) {
  .page-nav,
  .page-number {
    width: 32px;
    height: 32px;
  }

  .page-numbers {
    gap: 4px;
  }

  .ellipsis {
    width: 20px;
  }
}

.toggle-container {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 0;
  padding: 0;
  border-radius: 200px;
  border: 1px solid #f0f1f2;
  box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.24);
  width: fit-content;
  position: relative;
  background: #ffffff;
  overflow: hidden;
}

.toggle-option {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  padding: 8px 16px;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  position: relative;

  font-family: "Mulish", sans-serif;
  font-weight: 600;
  font-size: 16px;
  line-height: 1.5em;
  text-align: center;
  white-space: nowrap;

  &:first-child {
    border-radius: 200px 0 0 200px;
  }

  &:last-child {
    border-radius: 0 200px 200px 0;
  }

  &:only-child {
    border-radius: 200px;
  }

  &:hover:not(.disabled):not(.selected) {
    background-color: rgba(0, 0, 0, 0.05);
  }

  &.selected {
    background: linear-gradient(131deg, #0084ff 33.91%, #03bdd4 100%);
    border: 1px solid #6c96f2;
    border-radius: 200px;
    position: relative;
    z-index: 1;
    margin: -1px;
  }

  &.disabled {
    cursor: not-allowed;
    opacity: 0.6;
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .toggle-option {
    padding: 6px 12px;
    font-size: 14px;
  }
}

@media (max-width: 576px) {
  .toggle-option {
    padding: 6px 10px;
    font-size: 13px;
  }
}

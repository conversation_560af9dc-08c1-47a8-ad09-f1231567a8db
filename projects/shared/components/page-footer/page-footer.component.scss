.page-footer {
  width: 100%;
  padding: 12px 0;
  margin-top: 20px;
  // background: linear-gradient(102.14deg, rgba(255, 255, 255, 0.8) 1.07%, rgba(255, 255, 255, 0.9) 98.01%);
  // backdrop-filter: blur(8px);
  // -webkit-backdrop-filter: blur(8px);
  // transition: all 0.3s ease;
  // border-top: 1px solid rgba(230, 230, 230, 0.5);
  // box-shadow: 0 -4px 10px rgba(0, 0, 0, 0.03);
  
  /* Fix for border-radius causing cut-off */
  border-radius: 0;
  position: relative;
  z-index: 200;
}

.footer-content {
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 16px;
  display: flex;
  justify-content: center;
}

@media (max-width: 768px) {
  .page-footer {
    padding: 8px 0;
  }
  
  .footer-content {
    padding: 0 12px;
  }
} 
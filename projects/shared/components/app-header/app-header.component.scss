// ========================================
// SHARED APP HEADER COMPONENT STYLES
// ========================================

// ========================================
// 1. THEME VARIABLES & HOST CONFIGURATION
// ========================================

:host {
  // Light theme variables (default)
  --header-bg: #ffffff;
  --header-text: #374151;
  --header-border: rgba(0, 0, 0, 0.08);
  --nav-bg: #ffffff;
  --nav-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  --nav-text: #000000;
  --nav-arrow: #222222;
  --dropdown-bg: #ffffff;
  --dropdown-text: #374151;
  --dropdown-border: #e5e7eb;
  --dropdown-hover: #f3f4f6;

  // Dark theme variables
  &.theme-dark,
  :host-context(.dark-theme) & {
    --header-bg: #1f2937;
    --header-text: #f9fafb;
    --header-border: rgba(255, 255, 255, 0.1);
    --nav-bg: #374151;
    --nav-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    --nav-text: #ffffff;
    --nav-arrow: #d1d5db;
    --dropdown-bg: #374151;
    --dropdown-text: #f9fafb;
    --dropdown-border: #4b5563;
    --dropdown-hover: #4b5563;
  }

  // Host layout
  display: block;
  padding: 1.5rem 0 0 0;
  margin: 0;
  width: 100%;
  position: relative;

  @media (min-width: 1200px) {
    padding-top: 0;
  }

  @media (min-width: 1400px) {
    padding-top: 0;
  }
}

// ========================================
// 2. CORE LAYOUT OVERRIDES
// ========================================

::ng-deep .outer-box.light {
  background-color: transparent !important;
  box-shadow: none !important;
  margin: 0 !important;
  min-height: auto !important;
  width: 100% !important;
}

::ng-deep awe-header {
  width: 100% !important;
  margin: 0 !important;
  padding: 0 !important;
  background: var(--header-bg) !important;
  color: var(--header-text) !important;
  border-bottom: 1px solid var(--header-border) !important;

  .header-content {
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important; // Three sections: left, center, right
    width: 100% !important;
    height: 60px !important;
    min-height: 60px !important;
    padding: 0 1rem !important; // Base padding
    box-sizing: border-box !important;

    @media (max-width: 768px) {
      height: 56px !important;
      min-height: 56px !important;
      padding: 0 0.5rem !important;
    }
  }

  [left-content] {
    flex: 0 0 auto !important; // Fixed size - don't grow or shrink
    width: 240px !important; // Fixed width for logo section
    height: 60px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: flex-start !important;
    overflow: hidden !important;
    z-index: 10 !important;

    @media (max-width: 1200px) {
      width: 220px !important;
    }

    @media (max-width: 768px) {
      width: 200px !important;
      height: 56px !important;
    }

    @media (max-width: 480px) {
      width: 180px !important;
    }
  }

[center-content] {
  flex: 1 1 auto !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  overflow: visible !important;
  padding-top: 0px !important;
  padding-left: 2rem !important;
  padding-right: 2rem !important;
  margin-top: 0 !important;   // This avoids the space issue
  margin-bottom: 2rem !important;

  @media (max-width: 1400px) {
    padding-left: 1.5rem !important;
    padding-right: 1.5rem !important;
  }

  @media (max-width: 1200px) {
    padding-left: 1rem !important;
    padding-right: 1rem !important;
  }

  @media (max-width: 768px) {
    padding-left: 0.5rem !important;
    padding-right: 0.5rem !important;
    height: 56px !important;
  }

  @media (max-width: 480px) {
    padding-left: 0.25rem !important;
    padding-right: 0.25rem !important;
  }
}

 [right-content] {
  flex: 0 0 auto;
  width: clamp(150px, 25vw, 18px); // min 180px, max 360px, flexible between
  height: auto;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
  overflow: visible;
  padding: 0 1rem;
  gap: 0.75rem;

  // Optional height fine-tuning
  min-height: 48px;
  max-height: 64px;
}

}

.center-container {
  margin-top: 0 !important;
  padding-top: 0 !important;
  position: relative;
  z-index: 0;
}

/* =======================
   SHARED STYLES
======================= */
.header-shadow {
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  background: #215ad6;
  border-radius: 0 0 60px 60px;
  filter: blur(10px);
  opacity: 0.22;
  z-index: 1;
  pointer-events: none;
  transition: width 0.3s, height 0.3s;
}

/* =======================
   CENTER CONTENT STYLES
======================= */
.center-content-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  position: relative;
  z-index: 3;
}

.center-content-text {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--header-text);
  text-align: center;
  white-space: nowrap;

  &.default-center-text {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    letter-spacing: -0.01em;
  }

  // Theme-aware styling
  :host-context(.dark-theme) & {
    color: var(--header-text);
  }

  // Responsive design
  @media (max-width: 768px) {
    font-size: 1rem;
    font-weight: 500;
  }

  @media (max-width: 480px) {
    font-size: 0.875rem;
  }
}



/* Experience Studio specific header text styling */
.experience-studio-header-text {
  font-size: 1.5rem;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);//todo-> uncomment for new version
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  letter-spacing: -0.02em;

  // Dark theme gradient
  :host-context(.dark-theme) & {
    background: var(--main-title);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  // Responsive adjustments
  @media (max-width: 768px) {
    font-size: 1.25rem;
    font-weight: 600;
  }

  @media (max-width: 480px) {
    font-size: 1rem;
    font-weight: 600;
  }
}

.experience-studio-header-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 1rem;
}

.header-wrapper {
  position: relative;
  z-index: 2;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 0 auto;
  padding: 0 1rem;
   width: 100%;
  max-width: 1280px;
  background: #fff;
  clip-path: url(#headerClip);
  box-sizing: border-box;
  transition: width 0.3s, padding 0.3s, min-height 0.3s;
}

/* =======================
   PROJECT: Console
======================= */
.header-shadow.console-shadow {
  width: 797px;
  max-width: 797px;
  height: 53px;
}
.header-wrapper.console-wrapper {
  width: 850px;
  max-width: 850px;
  min-height: 50px;
}

/* =======================
   PROJECT: Elder Wand / Launchpad
======================= */
.header-shadow.elder-wand-shadow {
  width: 555px;
  max-width: 555px;
  height: 53px;
}
.header-wrapper.elder-wand-wrapper {
  width: 580px;
  max-width: 580px;
  min-height: 50px;
}

/* =======================
   PROJECT: Experience Studio
======================= */
.header-shadow.experience-studio-shadow {
  top: 0px;
  width: 755px;
  max-width: 755px;
  height: 53px;
}
.header-wrapper.experience-studio-wrapper {
  width: 700px;
  max-width: 700px;
  min-height: 50px;
}

/* =======================
   PROJECT: Product Studio
======================= */
.header-shadow.product-studio-shadow {
  width: 465px;
  max-width: 465px;
  height: 53px;
}
.header-wrapper.product-studio-wrapper {
  width: 485px;
  max-width: 485px;
  min-height: 50px;
}

/* =======================
   RESPONSIVE: header-shadow
======================= */
@media (max-width: 1200px) {
  .header-shadow.console-shadow {
    width: 98vw;
    max-width: 720px;
    height: 80px;
  }

  .header-shadow.elder-wand-shadow,
  .header-shadow.experience-studio-shadow {
    width: 98vw;
    max-width: 620px;
    height: 70px;
  }

  .header-shadow.product-studio-shadow {
    width: 98vw;
    max-width: 420px;
    height: 60px;
  }
}

@media (max-width: 768px) {
  .header-shadow.console-shadow,
  .header-shadow.elder-wand-shadow,
  .header-shadow.experience-studio-shadow,
  .header-shadow.product-studio-shadow {
    width: 99vw;
    max-width: 100vw;
    height: 40px;
  }
}

@media (max-width: 480px) {
  .header-shadow.console-shadow,
  .header-shadow.elder-wand-shadow,
  .header-shadow.experience-studio-shadow,
  .header-shadow.product-studio-shadow {
    width: 100vw;
    max-width: 300px;
    height: 28px;
  }
}

/* =======================
   RESPONSIVE: header-wrapper
======================= */
@media (max-width: 1200px) {
  .header-wrapper.console-wrapper {
    width: 96vw;
    max-width: 700px;
    min-height: 70px;
    padding: 0 16px;
  }

  .header-wrapper.elder-wand-wrapper,
  .header-wrapper.experience-studio-wrapper {
    width: 96vw;
    max-width: 600px;
    min-height: 70px;
    padding: 0 16px;
  }

  .header-wrapper.product-studio-wrapper {
    width: 96vw;
    max-width: 400px;
    min-height: 70px;
    padding: 0 16px;
  }
}

@media (max-width: 768px) {
  .header-wrapper.console-wrapper,
  .header-wrapper.elder-wand-wrapper,
  .header-wrapper.experience-studio-wrapper,
  .header-wrapper.product-studio-wrapper {
    width: 99vw;
    max-width: 100vw;
    min-height: 50px;
    padding: 0 8px;
  }
}

@media (max-width: 480px) {
  .header-wrapper.console-wrapper,
  .header-wrapper.elder-wand-wrapper,
  .header-wrapper.experience-studio-wrapper,
  .header-wrapper.product-studio-wrapper {
    width: 100vw;
    max-width: 300px;
    min-height: 36px;
    padding: 0 2px;
  }
}



// ========================================
// 4. LOGO STYLING
// ========================================

.header-logo {
  max-height: 40px !important;
  max-width: 160px !important;
  width: auto !important;
  height: auto !important;
  padding: 0 0.5rem !important;
  margin: 0 !important; // Remove margin since flex handles alignment

  @media (min-width: 1200px) {
    max-height: 50px !important;
    padding: 0 !important;
  }

  @media (min-width: 1400px) {
    max-height: 40px !important;
  }

  @media (max-width: 768px) {
    max-height: 28px !important;
    padding: 0 0.25rem !important;
  }
}

// ========================================
// 4.1 ANIMATED LOGO STYLING
// ========================================

.animated-logo-container {
  min-width: 180px; // Reduced from 200px to fit better
  max-width: 200px; // Add max-width to prevent overflow
  height: 80px; // Match parent container height
  position: relative;
  display: flex; // Changed from inline-block to flex
  align-items: center; // Center the logo vertically
  justify-content: flex-start; // Align logo to start
  cursor: pointer;
  perspective: 1000px; // Enable 3D transformations
  overflow: hidden; // Prevent any overflow

  @media (max-width: 1200px) {
    min-width: 160px;
    max-width: 180px;
  }

  @media (max-width: 768px) {
    min-width: 140px;
    max-width: 160px;
    height: 56px; // Match mobile header height
  }

  @media (max-width: 480px) {
    min-width: 120px;
    max-width: 140px;
  }

  // Single logo mode - optimized for static display
  &.single-logo-mode {
    min-width: 160px;
    max-width: 180px;
    cursor: default; // Remove pointer cursor for static logos

    @media (max-width: 1200px) {
      min-width: 140px;
      max-width: 160px;
    }

    @media (max-width: 768px) {
      min-width: 120px;
      max-width: 140px;
    }

    @media (max-width: 480px) {
      min-width: 100px;
      max-width: 120px;
    }
  }

  // Animation disabled mode
  &.animation-disabled {
    cursor: default;
    perspective: none;

    .animated-logo {
      transition: none !important;
      animation: none !important;

      &:hover {
        transform: none !important;
        filter: none !important;
      }
    }
  }

  .animated-logo {
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    transform: scale(1);
    opacity: 1;
    filter: brightness(1) saturate(1) blur(0px);
    transform-style: preserve-3d;

    &.logo-transitioning {
      opacity: 0.8;
      transition: opacity 0.3s ease-in-out;
    }

    &:hover {
      transform: scale(1.02); // Subtle scale on hover
      filter: brightness(1.05) saturate(1.1) blur(0px);
      transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    }

    // Static logo mode - no animations or transitions
    &.static-logo {
      transition: none !important;
      transform: scale(1) !important;
      opacity: 1 !important;
      filter: brightness(1) saturate(1) blur(0px) !important;
      transform-style: initial !important;
      animation: none !important;

      &:hover {
        transform: scale(1) !important;
        filter: brightness(1) saturate(1) blur(0px) !important;
        transition: none !important;
      }

      &.logo-transitioning {
        opacity: 1 !important;
        transition: none !important;
      }
    }
  }

  // Subtle glow effect on hover
  &:hover::before {
    content: "";
    position: absolute;
    top: -4px;
    left: -4px;
    right: -4px;
    bottom: -4px;
    border-radius: 12px;
    filter: blur(12px);
    opacity: 0;
    animation: logoGlow 0.4s ease-in-out forwards;
    z-index: -1;
    pointer-events: none;
  }
}

// Professional fade-in/fade-out animation
@keyframes logoFadeTransition {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0;
    transform: scale(0.95);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

// Smooth fade with subtle scale animation
@keyframes logoSmoothFade {
  0% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
  50% {
    opacity: 0;
    transform: scale(0.98) translateY(-2px);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

// Professional crossfade animation
@keyframes logoCrossfade {
  0% {
    opacity: 1;
    filter: brightness(1) blur(0px);
  }
  25% {
    opacity: 0.7;
    filter: brightness(1.1) blur(0.5px);
  }
  50% {
    opacity: 0;
    filter: brightness(1.2) blur(1px);
  }
  75% {
    opacity: 0.7;
    filter: brightness(1.1) blur(0.5px);
  }
  100% {
    opacity: 1;
    filter: brightness(1) blur(0px);
  }
}

// Animation style classes
.animated-logo {
  &.logo-transitioning {
    &.logo-fade {
      animation: logoFadeTransition 1.2s cubic-bezier(0.4, 0, 0.2, 1);
    }

    &.logo-smooth {
      animation: logoSmoothFade 1s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    }

    &.logo-crossfade {
      animation: logoCrossfade 1.4s cubic-bezier(0.23, 1, 0.32, 1);
    }

    // Subtle pulse animation for single logo
    &.logo-pulse {
      animation: logoPulse 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    }
  }
}

// Subtle pulse animation for single logo
@keyframes logoPulse {
  0% {
    opacity: 1;
    transform: scale(1);
    filter: brightness(1);
  }
  50% {
    opacity: 0.9;
    transform: scale(1.01);
    filter: brightness(1.05);
  }
  100% {
    opacity: 1;
    transform: scale(1);
    filter: brightness(1);
  }
}

// Glow effect animation
@keyframes logoGlow {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

// Studio-specific logo hints (optional: show which studio is current)
.animated-logo-container::after {
  content: attr(data-studio);
  position: absolute;
  bottom: -20px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 10px;
  font-weight: 600;
  color: var(--nav-arrow);
  opacity: 0;
  transition: opacity 0.2s ease;
  pointer-events: none;
  white-space: nowrap;
}

.animated-logo-container:hover::after {
  opacity: 0.7;
}

// Studio indicator dots
.studio-indicators {
  position: absolute;
  bottom: -30px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.3s ease;

  .studio-dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: var(--nav-arrow);
    opacity: 0.4;
    transition: all 0.3s ease;

    &.active {
      opacity: 1;
      background: linear-gradient(135deg, #8b5cf6, #ec4899);
      transform: scale(1.3);
    }
  }
}

.animated-logo-container:hover .studio-indicators {
  opacity: 1;
}

// Responsive adjustments for animated logo
@media (max-width: 768px) {
  .animated-logo-container {
    .animated-logo {
      &:hover {
        transform: scale(1.02); // Reduced hover effect on mobile
      }

      &.logo-transitioning {
        transform: scale(0.98); // Subtle transition on mobile
      }
    }

    // Hide studio name hint on mobile
    &::after {
      display: none;
    }

    // Simpler hover effect on mobile
    &:hover::before {
      display: none;
    }
  }
}

@media (max-width: 480px) {
  .animated-logo-container .animated-logo {
    transition: all 0.2s ease; // Faster transitions on small screens
  }
}

// ========================================
// 5. NAVIGATION MENU
// ========================================

.nav-menu {
  position: relative;
  z-index: 2;
  width: 100%;
  height: 100%; // Take full height of center content
  display: flex;
  justify-content: center;
  align-items: center; // Center the nav items vertically
  // padding: 0 20px; // Add horizontal padding to prevent hover cutting

  @media (max-width: 1200px) {
    padding: 0 16px;
  }

  @media (max-width: 768px) {
    padding: 0 12px;
  }

  @media (max-width: 480px) {
    padding: 0 8px;
  }
}


.nav-items {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  gap: 10px;
  height: auto;
  margin: 0;
  background: transparent;
  border-radius: 0;
  min-width: fit-content;
  width: max-content;
  box-shadow: none;
  border: none;
  padding: 0;
}

@media (max-width: 1200px) {
  .nav-items {
    gap: 18px;
  }
}
@media (max-width: 768px) {
  .nav-items {
    gap: 12px;
  }
}
@media (max-width: 480px) {
  .nav-items {
    gap: 8px;
    flex-wrap: wrap;
    justify-content: center;
  }
}

.nav-item-wrapper {
  position: relative;
  z-index: 5;
}

// ========================================
// 6. NAVIGATION ITEMS OVERRIDE
// ========================================

// ::ng-deep shared-nav-item {
//   .nav-item-container {
//     position: relative;
//   }

//   .nav-item {
//     display: flex;
//     align-items: center;
//     font-size: 16px;
//     font-weight: 500;
//     cursor: pointer;
//     transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
//     padding: 8px;
//     border-radius: 24px;
//     color: #64748b;
//     background: transparent;
//     border: none;
//     white-space: nowrap;
//     min-height: 40px;
//     position: relative;
//     overflow: visible; // Changed from hidden to visible for hover effects
//     margin: 2px; // Add small margin to prevent hover shadow clipping

//     // Responsive adjustments for dynamic nav width
//     @media (max-width: 1400px) {
//       padding: 9px 14px;
//       font-size: 15px;
//       gap: 6px;
//     }

//     @media (max-width: 1200px) {
//       padding: 8px 12px;
//       font-size: 14px;
//       gap: 6px;
//       min-height: 36px;
//     }

//     @media (max-width: 768px) {
//       padding: 6px 10px;
//       font-size: 13px;
//       gap: 4px;
//       min-height: 32px;
//       border-radius: 16px;
//       margin: 1px; // Reduced margin on smaller screens
//     }

//     @media (max-width: 480px) {
//       padding: 4px 8px;
//       font-size: 12px;
//       gap: 3px;
//       min-height: 28px;
//       border-radius: 12px;
//       margin: 1px;
//     }

//     // &:hover {
//     //   background: rgba(59, 130, 246, 0.08);
//     //   color: #3b82f6;
//     //   transform: translateY(-1px);
//     //   box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
//     //   z-index: 10; // Ensure hover state appears above other elements
//     // }

//     &.selected {
//       // background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
//       color: var(--rgb-brand-primary);
//       font-weight: 600;
//       // box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);
//       // transform: translateY(-2px);

//       .dropdown-arrow svg {
//         color: var(--rgb-brand-primary);
//       }
//     }

//     &.disabled {
//       opacity: 0.4;
//       cursor: not-allowed;
//       pointer-events: none;
//       color: #94a3b8;

//       // &:hover {
//       //   background: transparent;
//       //   transform: none;
//       //   box-shadow: none;
//       // }
//     }

//     &:focus-visible {
//       outline: 2px solid #3b82f6;
//       outline-offset: 2px;
//     }

//     .item-icon {
//       display: flex;
//       align-items: center;
//       justify-content: center;
//       width: 20px;
//       height: 20px;
//       transition: all 0.3s ease;

//       @media (max-width: 768px) {
//         width: 18px;
//         height: 18px;
//       }

//       @media (max-width: 480px) {
//         width: 16px;
//         height: 16px;
//       }
//     }

//     .nav-icon {
//       width: 20px;
//       height: 20px;
//       object-fit: contain;
//       transition: all 0.3s ease;

//       @media (max-width: 768px) {
//         width: 18px;
//         height: 18px;
//       }

//       @media (max-width: 480px) {
//         width: 16px;
//         height: 16px;
//       }
//     }

//     .item-label {
//       font-family: "Inter", "Segoe UI", sans-serif;
//       font-size: 15px;
//       letter-spacing: -0.01em;
//       transition: all 0.3s ease;

//       @media (max-width: 1200px) {
//         font-size: 14px;
//       }

//       @media (max-width: 768px) {
//         font-size: 13px;
//       }

//       @media (max-width: 480px) {
//         font-size: 12px;
//         letter-spacing: -0.02em;
//       }
//     }

//     .dropdown-arrow {
//       display: flex;
//       align-items: center;
//       justify-content: center;
//       width: 16px;
//       height: 16px;
//       margin-left: 4px;
//       transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);

//       &.open {
//         transform: rotate(180deg);
//       }

//       svg {
//         width: 14px;
//         height: 14px;
//         transition: color 0.3s ease;
//       }
//     }

//     // Removed click glow animation
//   }
// }

// ========================================
// 7. USER INFO CONTAINER
// ========================================

.user-info-container {
  position: absolute; // Make it escape flex-based flow
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  gap: 12px;
  z-index: 10;
  backdrop-filter: blur(10px);
  border-radius: 10px;
  padding: 4px 12px;
  height: 40px;

  @media (min-width: 1200px) {
    gap: 6px;
  }

  @media (max-width: 768px) {
    gap: 8px;
    height: 48px;

    > * {
      height: 36px;
    }
  }
}


// ========================================
// 8. ORGANIZATION SELECTOR
// ========================================

.org-path-dropdown-container {
  position: relative;
  display: inline-block;
  z-index: 1; // Ensure stacking context for popovers
}

.org-path-trigger {
  display: flex;
  align-items: center;
  justify-content: center; // Center since there's no text or arrow now
  background: var(--nav-bg) !important;
  border-radius: 50%;
  padding: 0 !important;
  width: 40px;
  height: 40px;
  box-shadow: var(--nav-shadow) !important;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid var(--header-border) !important;
  overflow: hidden;

  &:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    transform: translateY(-1px);
  }

  .org-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: transparent;
    width: 100%;
    height: 100%;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-radius: 50%;
    }
  }
}

// Responsive adjustments (optional, if you still want it to shrink slightly)
@media (max-width: 768px) {
  .org-path-trigger {
    width: 40px;
    height: 40px;
  }
}


.org-path-popover {
  position: absolute;
  top: 100%;
  left: 0;
  margin-top: 8px;
  z-index: 10000; // Increased from 20 to ensure it appears above all page content
  background: var(--dropdown-bg) !important;
  border-radius: 16px;
  box-shadow:
    0 20px 60px rgba(0, 0, 0, 0.15),
    0 0 0 3px rgba(59, 130, 246, 0.3); // Added blue border for visibility
  border: 1px solid var(--dropdown-border) !important;
  padding: 24px;
  overflow-y: auto;
  overflow-x: hidden;
  max-height: 80vh;
  max-width: 400px;
  // min-width: 360px; // Uncommented to ensure minimum size
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(20px);

  &.right {
    left: auto;
    right: 0;
  }

  &.left {
    left: 0;
    right: auto;
  }

  .filter-config-title {
    font-size: 20px;
    font-weight: 700;
    margin-bottom: 20px;
    color: var(--dropdown-text) !important;
  }

  .dropdown-row-vertical {
    display: flex;
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
    width: 100%;
  }

  .filter-label {
    display: block;
    font-weight: 600;
    font-size: 14px;
    color: var(--dropdown-text) !important;
    margin-bottom: 6px;

    &.required::after {
      content: " *";
      color: #ef4444;
      font-weight: bold;
    }
  }

  .popover-actions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    width: 100%;
    margin-top: 24px;
  }

  @media (max-width: 768px) {
    max-width: 95vw;
    min-width: 300px;
    padding: 20px;
  }

  @media (max-width: 480px) {
    max-width: 98vw;
    min-width: 280px;
    padding: 16px;
    margin-top: 4px;

    .filter-config-title {
      font-size: 18px;
      margin-bottom: 16px;
    }

    .dropdown-row-vertical {
      gap: 12px;
    }

    .popover-actions {
      margin-top: 20px;
    }
  }
}

.org-path-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: transparent;
  z-index: 9999; // Increased from 19 to be just below the popover (10000)
  cursor: default;
}

// ========================================
// 9. APP DRAWER
// ========================================

.app-drawer-container {
  position: relative;
  display: flex;
  align-items: center;

  .app-drawer-trigger {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    color: var(--nav-arrow) !important;

    // &:hover {
    //   background: var(--dropdown-hover) !important;
    //   color: var(--dropdown-text) !important;
    // }

    &.active {
      background: rgba(139, 92, 246, 0.1);
      color: #8b5cf6;
    }

    svg {
      display: block;
    }
  }

  .app-drawer-dropdown {
    position: absolute;
    top: calc(100% + 8px);
    right: 0;
    z-index: 1000;
    background: var(--dropdown-bg) !important;
    border-radius: 16px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
    border: 1px solid var(--dropdown-border) !important;
    width: 260px;
    max-width: calc(100vw - 40px);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(20px);
    overflow: hidden;

    &.visible {
      opacity: 1;
      visibility: visible;
      transform: translateY(0);
    }

    .app-drawer-content {
      padding: 0;
      overflow: hidden;
    }

    .app-drawer-header {
      padding: 20px 24px 16px 24px;
      background: linear-gradient(135deg, #8b5cf6 0%, #ec4899 100%);
      color: white;

      h3 {
        margin: 0;
        font-size: 18px;
        font-weight: 700;
        text-align: center;
      }
    }

    .app-drawer-grid {
      display: flex;
      flex-direction: column;
      gap: 8px;
      padding: 16px 20px 20px 20px;
    }

    .app-drawer-item {
      display: flex;
      align-items: center;
      padding: 12px;
      border-radius: 12px;
      cursor: pointer;
      transition: all 0.2s ease;
      border: 1px solid transparent;

      // &:hover {
      //   background: var(--dropdown-hover) !important;
      //   border-color: rgba(139, 92, 246, 0.2);
      //   transform: translateY(-1px);
      //   box-shadow: 0 4px 12px rgba(139, 92, 246, 0.1);
      // }

      .app-icon {
        margin-right: 12px;
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: var(--dropdown-hover) !important;
        border-radius: 10px;
        flex-shrink: 0;

        img {
          width: 24px;
          height: 24px;
          object-fit: contain;
        }
      }

      .app-info {
        flex: 1;
        min-width: 0;

        .app-name {
          font-size: 14px;
          font-weight: 600;
          color: var(--dropdown-text) !important;
          margin-bottom: 2px;
          word-wrap: break-word;
          overflow-wrap: break-word;
        }

        .app-description {
          font-size: 11px;
          color: var(--nav-arrow) !important;
          line-height: 1.3;
          word-wrap: break-word;
          overflow-wrap: break-word;
          white-space: normal;
        }
      }
    }

    @media (max-width: 768px) {
      width: 280px;
      max-width: calc(100vw - 20px);
      right: -10px;

      .app-drawer-header {
        padding: 16px 20px 12px 20px;

        h3 {
          font-size: 16px;
        }
      }

      .app-drawer-grid {
        gap: 6px;
        padding: 12px 16px 16px 16px;
      }

      .app-drawer-item {
        padding: 10px;

        .app-icon {
          width: 36px;
          height: 36px;

          img {
            width: 20px;
            height: 20px;
          }
        }

        .app-info .app-name {
          font-size: 13px;
        }

        .app-info .app-description {
          font-size: 10px;
        }
      }
    }

    @media (max-width: 480px) {
      width: 260px;
      max-width: calc(100vw - 10px);
      right: -5px;

      .app-drawer-grid {
        padding: 10px 12px 12px 12px;
      }
    }
  }
}

// ========================================
// 10. THEME TOGGLE
// ========================================

.theme-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);

  // &:hover {
  //   background: rgba(255, 255, 255, 1);
  //   transform: scale(1.05);
  //   box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  // }

  img {
    width: 20px;
    height: 20px;
    transition: transform 0.3s ease;
  }

  &:active img {
    transform: scale(0.95);
  }

  &.disabled {
    cursor: not-allowed;
    opacity: 0.5;
    pointer-events: none;

    img {
      filter: grayscale(100%);
    }
  }
}

// ========================================
// 11. PROFILE DROPDOWN
// ========================================

.profile-container {
  position: relative;
  display: flex;
  align-items: center;

  .profile-trigger {
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    border-radius: 50%;
    transition: all 0.2s ease;
    padding: 2px;

    // &:hover {
    //   background: rgba(0, 0, 0, 0.05);
    // }

    &.active {
      background: rgba(0, 0, 0, 0.1);
    }

    .profile-avatar {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      object-fit: cover;
      border: 2px solid rgba(255, 255, 255, 0.2);
    }
  }

  .profile-dropdown {
    position: absolute;
    top: calc(100% + 8px);
    right: 0;
    z-index: 1000;
    background: var(--dropdown-bg) !important;
    border-radius: 16px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
    border: 1px solid var(--dropdown-border) !important;
    min-width: 320px;
    max-width: 400px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(20px);

    &.visible {
      opacity: 1;
      visibility: visible;
      transform: translateY(0);
    }

    .profile-dropdown-content {
      padding: 0;
      overflow: hidden;
    }

    .profile-user-info {
      display: flex;
      align-items: flex-start;
      gap: 12px;
      padding: 24px 24px 20px 24px;
      background: linear-gradient(135deg, #8b5cf6 0%, #ec4899 100%);
      color: white;
      position: relative;

      .profile-avatar-large {
        flex-shrink: 0;

        img {
          width: 56px;
          height: 56px;
          border-radius: 50%;
          object-fit: cover;
          border: 3px solid rgba(255, 255, 255, 0.3);
        }
      }

      .profile-details {
        flex: 1;
        min-width: 0;

        .profile-name {
          font-size: 18px;
          font-weight: 700;
          margin-bottom: 4px;
          color: white;
        }

        .profile-designation {
          font-size: 14px;
          font-weight: 500;
          opacity: 0.9;
          margin-bottom: 2px;
          color: rgba(255, 255, 255, 0.9);
        }

        .profile-email {
          font-size: 13px;
          opacity: 0.8;
          color: rgba(255, 255, 255, 0.8);
          word-break: break-word;
        }
      }

      .profile-close-btn {
        position: absolute;
        top: 16px;
        right: 16px;
        background: none;
        border: none;
        color: rgba(255, 255, 255, 0.8);
        cursor: pointer;
        padding: 4px;
        border-radius: 4px;
        transition: all 0.2s ease;

        // &:hover {
        //   color: white;
        //   background: rgba(255, 255, 255, 0.1);
        // }

        svg {
          display: block;
        }
      }
    }

    .profile-divider {
      height: 1px;
      background: var(--dropdown-border) !important;
      margin: 0;
    }

    .profile-section {
      padding: 16px 24px;

      .profile-section-header {
        margin-bottom: 12px;

        .section-title {
          font-size: 14px;
          font-weight: 600;
          color: var(--dropdown-text) !important;
        }
      }
    }

    .theme-toggle-container {
      display: flex;
      gap: 8px;

      &.disabled {
        opacity: 0.5;
        pointer-events: none;
      }

      .theme-option {
        flex: 1;
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 10px 12px;
        border: 1px solid var(--dropdown-border) !important;
        border-radius: 8px;
        background: var(--dropdown-bg) !important;
        color: var(--nav-arrow) !important;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;

        // &:hover {
        //   border-color: var(--dropdown-border) !important;
        //   background: var(--dropdown-hover) !important;
        // }

        &.active {
          border-color: #8b5cf6;
          background: var(--dropdown-hover) !important;
          color: #8b5cf6;

          svg {
            color: #8b5cf6;
          }
        }

        &.disabled {
          cursor: not-allowed;
          opacity: 0.5;
          pointer-events: none;

          svg {
            filter: grayscale(100%);
          }
        }

        svg {
          flex-shrink: 0;
          color: currentColor;
        }
      }
    }

    .language-options {
      display: flex;
      flex-direction: column;
      gap: 4px;

      &.disabled {
        opacity: 0.5;
        pointer-events: none;
      }

      .language-option {
        display: flex;
        align-items: center;
        width: 100%;
        padding: 10px 12px;
        border: none;
        border-radius: 6px;
        background: transparent;
        color: var(--nav-arrow) !important;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        text-align: left;

        // &:hover {
        //   background: var(--dropdown-hover) !important;
        //   color: var(--dropdown-text) !important;
        // }

        &.active {
          background: rgba(139, 92, 246, 0.1) !important;
          color: #8b5cf6;
          font-weight: 600;
        }

        &.disabled {
          cursor: not-allowed;
          opacity: 0.5;
          pointer-events: none;
        }
      }
    }

    .profile-actions {
      padding: 16px 24px 24px 24px;

      .profile-action-item {
        display: flex;
        align-items: center;
        gap: 12px;
        width: 100%;
        padding: 12px 16px;
        border: none;
        border-radius: 8px;
        background: transparent;
        color: #dc2626;
        font-size: 14px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.2s ease;

        // &:hover {
        //   background: #fef2f2;
        //   color: #b91c1c;
        // }

        svg {
          flex-shrink: 0;
          color: currentColor;
        }
      }

      .logout-btn {
        justify-content: center;
        background: linear-gradient(135deg, #8b5cf6 0%, #ec4899 100%);
        color: white;

        // &:hover {
        //   background: linear-gradient(135deg, #7c3aed 0%, #db2777 100%);
        //   transform: translateY(-1px);
        //   box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
        // }
      }
    }

    @media (max-width: 768px) {
      min-width: 280px;
      right: -20px;

      .profile-user-info {
        padding: 20px;

        .profile-avatar-large img {
          width: 48px;
          height: 48px;
        }

        .profile-details .profile-name {
          font-size: 16px;
        }
      }

      .profile-section {
        padding: 12px 20px;
      }

      .profile-actions {
        padding: 12px 20px 20px 20px;
      }
    }
  }
}

// ========================================
// 12. DROPDOWN PORTAL
// ========================================

.dropdown-portal-menu {
  position: fixed;
  z-index: 2000;
}

.dropdown-menu {
  background: var(--dropdown-bg) !important;
  border-radius: 16px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--dropdown-border) !important;
  min-width: 320px;
  padding: 8px;
  backdrop-filter: blur(20px);
  margin-top: 8px;
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &:hover {
    background: var(--dropdown-hover) !important;
    transform: translateX(2px);
  }
}

.dropdown-icon {
  width: 24px;
  height: 24px;
  object-fit: contain;
  flex-shrink: 0;
}

.dropdown-content {
  flex: 1;
}

.dropdown-label {
  font-weight: 600;
  font-size: 16px;
  color: var(--dropdown-text) !important;
  margin-bottom: 4px;
}

.dropdown-description {
  font-size: 14px;
  color: var(--nav-arrow) !important;
  line-height: 1.5;
}

// ========================================
// 13. RESPONSIVE DESIGN
// ========================================

@media (max-width: 900px) {
  .header-shadow,
  .nav-items {
    width: 98vw;
    min-width: unset;
    // padding: 8px 12px;
  }
}

@media (max-width: 600px) {
  .header-shadow,
  .nav-items {
    width: 100vw;
    min-width: unset;
    // padding: 6px 8px;
    gap: 4px;
  }

  .nav-items {
    min-height: 48px;
  }

  ::ng-deep shared-nav-item {
    flex: 1 1 auto; // Allow nav items to grow and shrink
    min-width: 0; // Allow shrinking below content size

    .nav-item {
      padding: 8px 12px;
      font-size: 14px;
      border-radius: 20px;
      white-space: nowrap; // Keep text on one line
      overflow: hidden; // Hide overflow if needed
      text-overflow: ellipsis; // Show ellipsis for long text

      .item-label {
        font-size: 14px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
}

@media (max-width: 768px) {
  ::ng-deep shared-nav-item .nav-item {
    padding: 6px 10px; // Reduced padding for mobile
    font-size: 13px; // Slightly smaller font

    .item-label {
      font-size: 13px;
    }
  }

  .user-info-container {
    gap: 8px;
  }
}

@media (max-width: 480px) {
  ::ng-deep shared-nav-item .nav-item {
    padding: 4px 8px; // Further reduced for very small screens
    font-size: 12px;

    .item-label {
      font-size: 12px;
    }
  }
}

// ========================================
// 14. ANIMATIONS
// ========================================

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.nav-items {
  animation: fadeInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

// ========================================
// DEBUG UTILITIES (can be enabled for layout debugging)
// ========================================

// Uncomment the following to visualize layout boundaries
/*
::ng-deep awe-header {
  [left-content] {
    border: 2px solid red !important;
    background: rgba(255, 0, 0, 0.1) !important;
  }

  [center-content] {
    border: 2px solid blue !important;
    background: rgba(0, 0, 255, 0.1) !important;
  }

  [right-content] {
    border: 2px solid green !important;
    background: rgba(0, 255, 0, 0.1) !important;
  }
}

.nav-items {
  border: 2px solid orange !important;
  background: rgba(255, 165, 0, 0.1) !important;
}

.animated-logo-container {
  border: 2px solid purple !important;
  background: rgba(128, 0, 128, 0.1) !important;
}
*/

@media (max-width: 1200px) {
  ::ng-deep #main-header .col-auto {
    width: 16% !important;
    padding: 0 !important;
  }

  ::ng-deep #main-header .center-content-wrapper {
    width: 68% !important;
    padding: 0 !important;
  }
}

@media (min-width: 1201px) {
  ::ng-deep #main-header .col-auto {
    width: 15% !important;
    padding: 0 !important;
  }

  ::ng-deep #main-header .center-content-wrapper {
    width: 68% !important;
    padding: 0 !important;
  }
}
